// Тест для проверки исправления навигации при conditional_switch
import flowDialogService from './services/flowDialogService.js';
import { Flow, FlowMessage, Dialog } from './models/index.js';
import { v4 as uuidv4 } from 'uuid';

async function testNavigationFix() {
    console.log('🧪 Testing navigation fix for conditional_switch...');
    
    try {
        // Создаем тестовый поток
        const testFlow = await Flow.create({
            id: uuidv4(),
            name: 'Test Navigation Flow',
            isStartingFlow: true,
            aiPrompt: 'Test prompt'
        });

        // Создаем сообщения
        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Первый вопрос? {да|нет}',
            step: 5
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Вы военнослужащий? {да|нет}',
            step: 6,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: { type: 'yes_no', value: 'да' },
                successAction: { goto: 'continue' },
                failAction: { goto: '#8' }
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Справка от командования? {да|нет}',
            step: 7
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Вы мужчина 18-30 лет? {да|нет}',
            step: 8,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: { type: 'yes_no', value: 'да' },
                successAction: { goto: 'continue' },
                failAction: { goto: 'fin' }
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Военный билет? {да|нет}',
            step: 9
        });

        // Создаем диалог на шаге 5
        const dialog = await Dialog.create({
            id: uuidv4(),
            flowId: testFlow.id,
            currentStep: 5,
            collectedData: {}
        });

        console.log('✅ Test setup completed');
        
        // Шаг 1: Отвечаем на шаг 5, должны перейти к шагу 6
        console.log('📋 Step 1: Answer step 5, should move to step 6');
        const result1 = await flowDialogService.processMessage(dialog.id, 'да');
        console.log('Result 1:', {
            step: result1.step,
            message: result1.message.substring(0, 30) + '...',
            isConditionalSwitch: result1.isConditionalSwitch
        });

        // Шаг 2: Отвечаем на шаг 6 (conditional_switch), должны перейти к шагу 8
        console.log('📋 Step 2: Answer step 6 with "нет", should navigate to step 8');
        const result2 = await flowDialogService.processMessage(dialog.id, 'нет');
        console.log('Result 2:', {
            step: result2.step,
            message: result2.message.substring(0, 30) + '...',
            completed: result2.completed,
            isConditionalSwitch: result2.isConditionalSwitch
        });

        // Проверяем результат
        if (result2.step === 8 && result2.message.includes('Вы мужчина 18-30 лет?') && !result2.completed) {
            console.log('✅ SUCCESS: Navigation works correctly!');
            console.log('   - User was navigated to step 8');
            console.log('   - Step 8 message was shown to user');
            console.log('   - Dialog is not completed yet');
        } else {
            console.log('❌ FAILED: Navigation not working as expected');
            console.log('   Expected: step=8, message contains "Вы мужчина 18-30 лет?", completed=false');
        }

        // Cleanup
        await dialog.destroy();
        await FlowMessage.destroy({ where: { flowId: testFlow.id } });
        await testFlow.destroy();

    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        console.error('Stack:', error.stack);
    }
}

testNavigationFix().then(() => {
    console.log('🏁 Test completed');
    process.exit(0);
}).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
});
