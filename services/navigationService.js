import logger from '../utils/logger.js';
import {Flow, FlowMessage} from '../models/index.js';

/**
 * Service for handling navigation logic in flow dialogs
 */
class NavigationService {
    constructor() {
        logger.info('Navigation service initialized');
    }

    /**
     * Parse goto value and determine navigation action
     * @param {string} gotoValue - The goto value to parse
     * @returns {Object} - Parsed navigation action
     */
    parseGoto(gotoValue) {
        if (!gotoValue || typeof gotoValue !== 'string') {
            throw new Error('Invalid goto value: must be a non-empty string');
        }

        const trimmedValue = gotoValue.trim();

        // Case 1: continue - continue linear execution
        if (trimmedValue === 'continue') {
            return {
                type: 'continue',
                flowId: null,
                step: null
            };
        }

        // Case 2: fin - finish dialog immediately
        if (trimmedValue === 'fin') {
            return {
                type: 'finish',
                flowId: null,
                step: null
            };
        }

        // Case 3: #step - jump to step within current flow
        if (trimmedValue.startsWith('#')) {
            const stepStr = trimmedValue.substring(1);
            const step = parseInt(stepStr);

            if (isNaN(step) || step < 0) {
                throw new Error(`Invalid step number in goto: ${trimmedValue}`);
            }

            return {
                type: 'step',
                flowId: null,
                step: step
            };
        }

        // Case 4 & 5: flowId or flowId#step
        if (trimmedValue.includes('#')) {
            // Case 5: flowId#step
            const parts = trimmedValue.split('#');
            if (parts.length !== 2) {
                throw new Error(`Invalid goto format: ${trimmedValue}`);
            }

            const [flowId, stepStr] = parts;
            const step = parseInt(stepStr);

            if (!flowId || isNaN(step) || step < 0) {
                throw new Error(`Invalid flowId#step format in goto: ${trimmedValue}`);
            }

            return {
                type: 'flow_step',
                flowId: flowId,
                step: step
            };
        } else {
            // Case 4: flowId - jump to beginning of another flow
            return {
                type: 'flow',
                flowId: trimmedValue,
                step: 0
            };
        }
    }

    /**
     * Execute navigation action
     * @param {Object} dialog - Current dialog object
     * @param {Object} navigationAction - Parsed navigation action
     * @returns {Promise<Object>} - Navigation result
     */
    async executeNavigation(dialog, navigationAction) {
        const {type, flowId, step} = navigationAction;

        logger.info(`🧭 Executing navigation: type=${type}, flowId=${flowId}, step=${step}`);

        switch (type) {
            case 'continue':
                return await this.continueCurrentFlow(dialog);

            case 'finish':
                return await this.finishDialog(dialog);

            case 'step':
                return await this.jumpToStep(dialog, dialog.flowId, step);

            case 'flow':
                return await this.jumpToFlow(dialog, flowId, 0);

            case 'flow_step':
                return await this.jumpToFlow(dialog, flowId, step);

            default:
                throw new Error(`Unknown navigation type: ${type}`);
        }
    }

    /**
     * Continue with current flow (move to next step)
     * @param {Object} dialog - Dialog object
     * @returns {Promise<Object>} - Navigation result
     */
    async continueCurrentFlow(dialog) {
        const nextStep = dialog.currentStep + 1;
        return await this.jumpToStep(dialog, dialog.flowId, nextStep);
    }

    /**
     * Jump to specific step in specified flow
     * @param {Object} dialog - Dialog object
     * @param {string} targetFlowId - Target flow ID
     * @param {number} targetStep - Target step number
     * @returns {Promise<Object>} - Navigation result
     */
    async jumpToStep(dialog, targetFlowId, targetStep) {
        try {
            // Verify target flow exists
            const targetFlow = await Flow.findByPk(targetFlowId);
            if (!targetFlow) {
                throw new Error(`Target flow ${targetFlowId} not found`);
            }

            // Find target message
            const targetMessage = await FlowMessage.findOne({
                where: {
                    flowId: targetFlowId,
                    step: targetStep
                }
            });

            if (!targetMessage) {
                // If no message found, complete the dialog
                logger.info(`No message found at step ${targetStep} in flow ${targetFlowId}, completing dialog`);
                return {
                    type: 'complete',
                    flowId: targetFlowId,
                    step: targetStep,
                    message: null,
                    flowSwitched: targetFlowId !== dialog.flowId
                };
            }

            // Update dialog
            const flowSwitched = targetFlowId !== dialog.flowId;
            await dialog.update({
                flowId: targetFlowId,
                currentStep: targetStep
            });

            logger.info(`Successfully navigated to flow ${targetFlowId}, step ${targetStep}`);

            return {
                type: 'message',
                flowId: targetFlowId,
                step: targetStep,
                message: targetMessage,
                flowSwitched: flowSwitched
            };

        } catch (error) {
            logger.error(`Error jumping to step ${targetStep} in flow ${targetFlowId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Jump to specific flow (convenience method)
     * @param {Object} dialog - Dialog object
     * @param {string} targetFlowId - Target flow ID
     * @param {number} targetStep - Target step number (default: 0)
     * @returns {Promise<Object>} - Navigation result
     */
    async jumpToFlow(dialog, targetFlowId, targetStep = 0) {
        return await this.jumpToStep(dialog, targetFlowId, targetStep);
    }

    /**
     * Finish dialog immediately
     * @param {Object} dialog - Dialog object
     * @returns {Promise<Object>} - Navigation result
     */
    async finishDialog(dialog) {
        logger.info(`🏁 Finishing dialog ${dialog.id} immediately`);

        return {
            type: 'complete',
            flowId: dialog.flowId,
            step: dialog.currentStep,
            message: null,
            flowSwitched: false
        };
    }

    /**
     * Validate goto value format
     * @param {string} gotoValue - The goto value to validate
     * @returns {boolean} - True if valid, false otherwise
     */
    validateGoto(gotoValue) {
        try {
            this.parseGoto(gotoValue);
            return true;
        } catch (error) {
            logger.warn(`Invalid goto value: ${gotoValue} - ${error.message}`);
            return false;
        }
    }
}

const navigationService = new NavigationService();
export default navigationService;
