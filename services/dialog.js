import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger.js';
import {Dialog, Message} from '../models/index.js';

class DialogService {
    constructor() {
        logger.info(`Dialog service initialized`);
    }

    /**
     * Create a new dialog
     * @returns {Promise<string>} - Dialog ID
     */
    async createDialog() {
        try {
            const dialog = await Dialog.create({
                id: uuidv4()
            });

            logger.info(`Created new dialog with ID: ${dialog.id}`);
            return dialog.id;
        } catch (error) {
            logger.error(`Error creating dialog: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get a dialog by ID
     * @param {string} dialogId - Dialog ID
     * @returns {Promise<Object|null>} - Dialog object or null if not found
     */
    async getDialog(dialogId) {
        try {
            const dialog = await Dialog.findByPk(dialogId, {
                include: [{model: Message, as: 'Messages'}]
            });

            if (!dialog) {
                logger.warn(`Dialog not found: ${dialogId}`);
                return null;
            }

            return dialog;
        } catch (error) {
            logger.error(`<PERSON>rror getting dialog ${dialogId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Add a message to a dialog
     * @param {string} dialogId - Dialog ID
     * @param {string} role - Message role (user, assistant, system)
     * @param {string} content - Message content
     * @returns {Promise<Object|null>} - Updated dialog or null if dialog not found
     */
    async addMessage(dialogId, role, content) {
        try {
            // Check if dialog exists
            const dialog = await Dialog.findByPk(dialogId);

            if (!dialog) {
                logger.warn(`Cannot add message, dialog not found: ${dialogId}`);
                return null;
            }

            // Create message
            await Message.create({
                id: uuidv4(),
                dialogId,
                role,
                content,
                timestamp: new Date()
            });

            // Update dialog's updatedAt timestamp
            await dialog.update({updatedAt: new Date()});

            // Get updated dialog with messages
            const updatedDialog = await this.getDialog(dialogId);

            logger.info(`Added ${role} message to dialog ${dialogId}`);
            return updatedDialog;
        } catch (error) {
            logger.error(`Error adding message to dialog ${dialogId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get messages from a dialog
     * @param {string} dialogId - Dialog ID
     * @param {number} limit - Maximum number of messages to return
     * @returns {Promise<Array|null>} - Array of messages or null if dialog not found
     */
    async getMessages(dialogId, limit = 0) {
        try {
            const dialog = await this.getDialog(dialogId);

            if (!dialog) {
                return null;
            }

            let messages = dialog.Messages || [];

            // Sort messages by timestamp
            messages = messages.sort((a, b) => a.timestamp - b.timestamp);

            if (limit > 0 && messages.length > limit) {
                messages = messages.slice(-limit);
            }

            return messages;
        } catch (error) {
            logger.error(`Error getting messages for dialog ${dialogId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get messages formatted for LLM
     * @param {string} dialogId - Dialog ID
     * @param {number} limit - Maximum number of messages to return
     * @returns {Promise<Array|null>} - Array of formatted messages or null if dialog not found
     */
    async getFormattedMessages(dialogId, limit = 0) {
        try {
            const messages = await this.getMessages(dialogId, limit);

            if (!messages) {
                return null;
            }

            return messages.map(message => ({
                role: message.role,
                content: message.content
            }));
        } catch (error) {
            logger.error(`Error getting formatted messages for dialog ${dialogId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Delete a dialog
     * @param {string} dialogId - Dialog ID
     * @returns {Promise<boolean>} - True if dialog was deleted, false otherwise
     */
    async deleteDialog(dialogId) {
        try {
            const dialog = await Dialog.findByPk(dialogId);

            if (!dialog) {
                logger.warn(`Cannot delete dialog, not found: ${dialogId}`);
                return false;
            }
            // Delete the dialog
            await dialog.destroy();

            logger.info(`Deleted dialog: ${dialogId}`);
            return true;
        } catch (error) {
            logger.error(`Error deleting dialog ${dialogId}: ${error.message}`);
            throw error;
        }
    }
}

// Create and export a singleton instance
const dialogService = new DialogService();
export default dialogService;
