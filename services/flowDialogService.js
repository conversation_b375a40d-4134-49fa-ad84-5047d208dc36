import {v4 as uuidv4} from 'uuid';
import logger from '../utils/logger.js';
import {Dialog, Flow, FlowMessage, Message} from '../models/index.js';
import aiService from './aiService.js';
import maskSystem from '../masks/index.js';
import conditionEvaluator from './conditionEvaluator.js';
import navigationService from './navigationService.js';

class FlowDialogService {
    constructor() {
        logger.info('Flow dialog service initialized');
    }

    /**
     * Start a new dialog with a specific flow
     * @param {string} flowId - Flow ID to use
     * @param dialog
     * @returns {Promise<Object>} - Created dialog
     */
    async startDialog(flowId, dialog) {
        try {
            // Check if flow exists
            const flow = await Flow.findByPk(flowId);
            if (!flow) {
                throw new Error(`Flow with ID ${flowId} not found`);
            }

            logger.info(`Started new dialog ${dialog.id} with flow ${flowId}`);

            // Get first message from flow
            const firstMessage = await FlowMessage.findOne({
                where: {flowId, step: 0},
                order: [['step', 'ASC']]
            });

            if (!firstMessage) {
                throw new Error(`No messages found in flow ${flowId}`);
            }

            // Удаляем маски из сообщения
            const cleanContent = maskSystem.removeMasks(firstMessage.content);

            // Add first bot message to dialog
            await Message.create({
                id: uuidv4(),
                dialogId: dialog.id,
                role: 'assistant',
                content: cleanContent,
                timestamp: new Date()
            });

            return {
                dialogId: dialog.id,
                message: cleanContent,
                step: 0
            };
        } catch (error) {
            logger.error(`Error starting dialog with flow ${flowId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process user message in a flow dialog
     * @param {string} dialogId - Dialog ID
     * @param {string} userMessage - User's message
     * @returns {Promise<Object>} - Next step info
     */
    async processMessage(dialogId, userMessage) {
        try {
            // Get dialog
            const dialog = await Dialog.findByPk(dialogId);
            if (!dialog) {
                throw new Error(`Dialog ${dialogId} not found`);
            }

            if (dialog.completed) {
                return {
                    completed: true,
                    message: "This dialog is already completed",
                    result: dialog.completionResult
                };
            }

            // Get current flow message
            const currentFlowMessage = await FlowMessage.findOne({
                where: { 
                    flowId: dialog.flowId,
                    step: dialog.currentStep
                }
            });

            if (!currentFlowMessage) {
                throw new Error(`No message found for step ${dialog.currentStep} in flow ${dialog.flowId}`);
            }

            await Message.create({
                id: uuidv4(),
                dialogId,
                role: 'user',
                content: userMessage,
                timestamp: new Date()
            });

            // Process user response
            const processedResponse = await this.processUserResponse(
                userMessage,
                currentFlowMessage.content
            );

            logger.info(`📝 PROCESSED RESPONSE: original="${userMessage}" → processed="${processedResponse}"`);

            // Извлекаем маски из сообщения бота
            const masks = maskSystem.extractMasks(currentFlowMessage.content);

            // Проверяем валидность ответа, если есть маски
            if (masks.length > 0) {
                const maskName = masks[0]; // Берем первую маску, если их несколько

                // Проверяем, является ли ответ валидным для данной маски
                const isValid = maskSystem.isValidResponse(processedResponse, maskName);

                if (!isValid) {
                    // Если ответ невалидный, возвращаем сообщение об ошибке
                    const errorMessage = maskSystem.getErrorMessage(maskName, currentFlowMessage.content);

                    // Добавляем сообщение об ошибке в диалог
                    await Message.create({
                        id: uuidv4(),
                        dialogId,
                        role: 'assistant',
                        content: errorMessage,
                        timestamp: new Date()
                    });

                    // Возвращаем ответ с сообщением об ошибке, не меняя текущий шаг
                    return {
                        dialogId,
                        message: errorMessage,
                        step: dialog.currentStep,
                        completed: false,
                        error: true
                    };
                }
            }

            // Update collected data
            const updatedData = {...dialog.collectedData};
            updatedData[`step_${dialog.currentStep}`] = {
                question: currentFlowMessage.content,
                answer: userMessage,
                processed: processedResponse
            };
            // Store last processed response for conditional switches
            updatedData.lastProcessedResponse = processedResponse;

            await dialog.update({collectedData: updatedData});

            // Check if CURRENT message is a conditional switch - evaluate after user response
            if (currentFlowMessage.messageType === 'conditional_switch') {
                return await this.handleConditionalSwitch(
                    dialog,
                    currentFlowMessage,
                    processedResponse,  // Use processed response from current step
                    updatedData
                );
            }

            // Move to next step
            const nextStep = dialog.currentStep + 1;
            const nextFlowMessage = await FlowMessage.findOne({
                where: {
                    flowId: dialog.flowId,
                    step: nextStep
                }
            });

            // If no more messages, complete the dialog
            if (!nextFlowMessage) {
                return await this.completeDialog(dialog);
            }

            // Update dialog step
            await dialog.update({currentStep: nextStep});

            // For conditional_switch messages, send the message first
            // The condition will be evaluated when the user responds
            const cleanContent = maskSystem.removeMasks(nextFlowMessage.content);

            // Add bot message to dialog
            await Message.create({
                id: uuidv4(),
                dialogId,
                role: 'assistant',
                content: cleanContent,
                timestamp: new Date()
            });

            return {
                dialogId,
                message: cleanContent,
                step: nextStep,
                completed: false,
                isConditionalSwitch: nextFlowMessage.messageType === 'conditional_switch'
            };
        } catch (error) {
            logger.error(`Error processing message in dialog ${dialogId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Handle conditional flow switching
     * @param {Object} dialog - Dialog object
     * @param {Object} conditionalFlowMessage - Conditional flow message with config
     * @param {string} processedResponse - User's processed response from previous step
     * @param {Object} collectedData - Updated collected data
     * @returns {Promise<Object>} - Next step info
     */
    async handleConditionalSwitch(dialog, conditionalFlowMessage, processedResponse, collectedData) {
        try {
            const { conditionConfig } = conditionalFlowMessage;

            if (!conditionConfig) {
                logger.error('Conditional switch message missing conditionConfig');
                throw new Error('Invalid conditional switch configuration');
            }

            // Validate new format
            if (!conditionConfig.successAction || !conditionConfig.failAction) {
                throw new Error('Invalid conditional switch configuration: successAction and failAction are required');
            }

            const successAction = conditionConfig.successAction;
            const failAction = conditionConfig.failAction;

            if (!successAction.goto || !failAction.goto) {
                throw new Error('Invalid conditional switch configuration: successAction and failAction must contain goto field');
            }

            const { condition } = conditionConfig;

            logger.info(`🔄 CONDITIONAL SWITCH: processedResponse="${processedResponse}" (type: ${typeof processedResponse})`);
            logger.info(`🔄 CONDITION CONFIG:`, JSON.stringify(conditionConfig, null, 2));

            // Evaluate condition against previous step's processed response
            const conditionMet = conditionEvaluator.evaluateCondition(condition, processedResponse);

            logger.info(`Condition evaluation result: ${conditionMet}`);

            // Determine action based on condition result
            const targetAction = conditionMet ? successAction : failAction;
            const gotoValue = targetAction.goto;

            logger.info(`Selected action goto: ${gotoValue}`);

            // Parse and execute navigation
            const navigationAction = navigationService.parseGoto(gotoValue);
            const navigationResult = await navigationService.executeNavigation(dialog, navigationAction);

            return await this.processNavigationResult(dialog, navigationResult, collectedData);

        } catch (error) {
            logger.error(`Error handling conditional switch: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process navigation result and return appropriate response
     * @param {Object} dialog - Dialog object
     * @param {Object} navigationResult - Result from navigation service
     * @param {Object} collectedData - Updated collected data
     * @returns {Promise<Object>} - Response object
     */
    async processNavigationResult(dialog, navigationResult, collectedData) {
        try {
            const { type, flowId, step, message, flowSwitched } = navigationResult;

            if (type === 'complete') {
                // Dialog completed
                return await this.completeDialog(dialog);
            }

            if (type === 'message') {
                // Always send the message to user first, regardless of type
                // The conditional logic will be handled when user responds
                const cleanContent = maskSystem.removeMasks(message.content);

                // Add bot message to dialog
                await Message.create({
                    id: uuidv4(),
                    dialogId: dialog.id,
                    role: 'assistant',
                    content: cleanContent,
                    timestamp: new Date()
                });

                return {
                    dialogId: dialog.id,
                    message: cleanContent,
                    step: step,
                    completed: false,
                    flowSwitched: flowSwitched,
                    newFlowId: flowSwitched ? flowId : undefined,
                    isConditionalSwitch: message.messageType === 'conditional_switch'
                };
            }

            throw new Error(`Unknown navigation result type: ${type}`);

        } catch (error) {
            logger.error(`Error processing navigation result: ${error.message}`);
            throw error;
        }
    }

    /**
     * Continue with current flow after conditional check
     * @param {Object} dialog - Dialog object
     * @returns {Promise<Object>} - Next step info
     */
    async continueCurrentFlow(dialog) {
        try {
            // Move to next step in current flow
            const nextStep = dialog.currentStep + 1;
            const nextFlowMessage = await FlowMessage.findOne({
                where: {
                    flowId: dialog.flowId,
                    step: nextStep
                }
            });

            // If no more messages, complete the dialog
            if (!nextFlowMessage) {
                return await this.completeDialog(dialog);
            }

            // Update dialog step
            await dialog.update({currentStep: nextStep});

            // Use next flow message
            const responseMessage = maskSystem.removeMasks(nextFlowMessage.content);

            // Add bot message to dialog
            await Message.create({
                id: uuidv4(),
                dialogId: dialog.id,
                role: 'assistant',
                content: responseMessage,
                timestamp: new Date()
            });

            return {
                dialogId: dialog.id,
                message: responseMessage,
                step: nextStep,
                completed: false
            };
        } catch (error) {
            logger.error(`Error continuing current flow: ${error.message}`);
            throw error;
        }
    }

    /**
     * Switch to a new flow
     * @param {Object} dialog - Dialog object
     * @param {string} targetFlowId - Target flow ID
     * @returns {Promise<Object>} - Next step info
     */
    async switchToFlow(dialog, targetFlowId) {
        try {
            // Verify target flow exists
            const targetFlow = await Flow.findByPk(targetFlowId);
            if (!targetFlow) {
                logger.error(`Target flow ${targetFlowId} not found`);
                throw new Error(`Target flow ${targetFlowId} not found`);
            }

            // Get first message from target flow
            const firstMessage = await FlowMessage.findOne({
                where: {flowId: targetFlowId, step: 0},
                order: [['step', 'ASC']]
            });

            if (!firstMessage) {
                logger.error(`No messages found in target flow ${targetFlowId}`);
                throw new Error(`No messages found in target flow ${targetFlowId}`);
            }

            // Update dialog to use new flow
            await dialog.update({
                flowId: targetFlowId,
                currentStep: 0
            });

            // Use first message from new flow
            const responseMessage = maskSystem.removeMasks(firstMessage.content);

            // Add bot message to dialog
            await Message.create({
                id: uuidv4(),
                dialogId: dialog.id,
                role: 'assistant',
                content: responseMessage,
                timestamp: new Date()
            });

            logger.info(`Successfully switched dialog ${dialog.id} to flow ${targetFlowId}`);

            return {
                dialogId: dialog.id,
                message: responseMessage,
                step: 0,
                completed: false,
                flowSwitched: true,
                newFlowId: targetFlowId
            };
        } catch (error) {
            logger.error(`Error switching to flow ${targetFlowId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process user response based on mask in bot message
     * @param {string} userResponse - User's response
     * @param {string} botMessage - Bot's message with potential mask
     * @returns {Promise<string>} - Processed response
     */
    async processUserResponse(userResponse, botMessage) {
        try {
            // Extract mask from bot message if present
            const maskMatch = botMessage.match(/{([^}]+)}/);
            if (!maskMatch) {
                // No mask, return original response
                return userResponse;
            }

            const maskType = maskMatch[1];

            // Используем систему масок для обработки ответа
            const processedResponse = await maskSystem.processMask(
                userResponse, maskType, botMessage
            );

            // Если обработка прошла успешно, возвращаем результат
            if (processedResponse !== null) {
                return processedResponse;
            }

            // Если не удалось обработать, возвращаем исходный ответ
            logger.warn(`Failed to process response with mask ${maskType}, returning original`);
            return userResponse;
        } catch (error) {
            logger.error(`Error processing user response: ${error.message}`);
            return userResponse;
        }
    }

    /**
     * Complete a dialog and evaluate results
     * @param {Object} dialog - Dialog object
     * @returns {Promise<Object>} - Completion result
     */
    async completeDialog(dialog) {
        try {
            // Get flow for AI prompt
            const flow = await Flow.findByPk(dialog.flowId);

            // Get all messages from dialog
            const messages = await Message.findAll({
                where: {dialogId: dialog.id},
                order: [['timestamp', 'ASC']]
            });

            // Format dialog for AI evaluation
            const formattedDialog = messages.map(m => ({
                role: m.role,
                content: m.content
            }));

            // Evaluate dialog with AI using flow.aiPrompt
            let result = "pass"; // По умолчанию считаем, что все успешно

            if (flow.aiPrompt) {
                // Вызываем AI для оценки диалога
                result = await aiService.evaluateDialog(formattedDialog, flow.aiPrompt, dialog.collectedData);
            }

            // Определяем финальное сообщение в зависимости от результата
            const finalMessage = result === "pass" ?
                (flow.successMessage || "Все необходимые документы в наличии. Пожалуйста, возьмите талончик на услугу.") :
                (flow.failureMessage || "Не все необходимые документы в наличии. Пожалуйста, подготовьте все документы и вернитесь.");

            // Update dialog as completed
            await dialog.update({
                completed: true,
                completionResult: result
            });

            // Добавляем финальное сообщение в диалог
            await Message.create({
                id: uuidv4(),
                dialogId: dialog.id,
                role: 'assistant',
                content: finalMessage,
                timestamp: new Date()
            });

            return {
                dialogId: dialog.id,
                completed: true,
                message: finalMessage,
                result: result
            };
        } catch (error) {
            logger.error(`Error completing dialog ${dialog.id}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Register a new mask handler
     * @param {string} maskType - Mask type identifier
     * @param {Function} handler - Handler function
     */
    registerMaskHandler(maskType, handler) {
        if (typeof handler !== 'function') {
            throw new Error('Handler must be a function');
        }

        // Эта функция теперь устарела, но оставлена для обратной совместимости
        logger.warn(`registerMaskHandler is deprecated, use mask system instead`);
    }
}

// Create and export a singleton instance
const flowDialogService = new FlowDialogService();
export default flowDialogService;