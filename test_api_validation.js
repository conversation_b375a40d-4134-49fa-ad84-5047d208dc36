// Тест для проверки валидации API с новым форматом
import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api';

async function testValidation() {
    console.log('🧪 Testing API validation with new format...');
    
    try {
        // Test 1: Validate new format condition
        console.log('\n📋 Test 1: Validate new format condition');
        const newFormatCondition = {
            conditionConfig: {
                condition: {
                    type: 'yes_no',
                    value: 'да'
                },
                successAction: {
                    goto: 'continue'
                },
                failAction: {
                    goto: '#8'
                }
            }
        };

        try {
            const response1 = await axios.post(`${API_BASE_URL}/flow/validate-condition`, newFormatCondition);
            console.log('✅ New format validation passed:', response1.data);
        } catch (error) {
            console.log('❌ New format validation failed:', error.response?.data || error.message);
        }

        // Test 2: Validate always_true condition
        console.log('\n📋 Test 2: Validate always_true condition');
        const alwaysTrueCondition = {
            conditionConfig: {
                condition: {
                    type: 'always_true'
                },
                successAction: {
                    goto: 'fin'
                },
                failAction: {
                    goto: 'continue'
                }
            }
        };

        try {
            const response2 = await axios.post(`${API_BASE_URL}/flow/validate-condition`, alwaysTrueCondition);
            console.log('✅ Always_true validation passed:', response2.data);
        } catch (error) {
            console.log('❌ Always_true validation failed:', error.response?.data || error.message);
        }

        // Test 3: Validate goto patterns
        console.log('\n📋 Test 3: Validate different goto patterns');
        const gotoPatterns = [
            'continue',
            'fin',
            '#5',
            'other-flow-id',
            'other-flow-id#3'
        ];

        for (const gotoValue of gotoPatterns) {
            const testCondition = {
                conditionConfig: {
                    condition: {
                        type: 'always_true'
                    },
                    successAction: {
                        goto: gotoValue
                    },
                    failAction: {
                        goto: 'continue'
                    }
                }
            };

            try {
                const response = await axios.post(`${API_BASE_URL}/flow/validate-condition`, testCondition);
                console.log(`✅ goto: "${gotoValue}" - valid`);
            } catch (error) {
                console.log(`❌ goto: "${gotoValue}" - invalid:`, error.response?.data?.error || error.message);
            }
        }

        // Test 4: Test invalid goto patterns
        console.log('\n📋 Test 4: Test invalid goto patterns');
        const invalidGotoPatterns = [
            '',
            'invalid#',
            '#invalid',
            'flow-id#',
            'flow id with spaces'
        ];

        for (const gotoValue of invalidGotoPatterns) {
            const testCondition = {
                conditionConfig: {
                    condition: {
                        type: 'always_true'
                    },
                    successAction: {
                        goto: gotoValue
                    },
                    failAction: {
                        goto: 'continue'
                    }
                }
            };

            try {
                const response = await axios.post(`${API_BASE_URL}/flow/validate-condition`, testCondition);
                console.log(`❌ goto: "${gotoValue}" - should be invalid but passed`);
            } catch (error) {
                console.log(`✅ goto: "${gotoValue}" - correctly rejected:`, error.response?.data?.error || error.message);
            }
        }

        // Test 5: Test old format for backward compatibility
        console.log('\n📋 Test 5: Test old format for backward compatibility');
        const oldFormatCondition = {
            conditionConfig: {
                condition: {
                    type: 'age_check',
                    operator: '<',
                    value: 18
                },
                successFlowId: 'continue',
                failFlowId: 'continue'
            }
        };

        try {
            const response5 = await axios.post(`${API_BASE_URL}/flow/validate-condition`, oldFormatCondition);
            console.log('✅ Old format validation passed:', response5.data);
        } catch (error) {
            console.log('❌ Old format validation failed:', error.response?.data || error.message);
        }

        // Test 6: Test mixed format (should fail)
        console.log('\n📋 Test 6: Test mixed format (should fail)');
        const mixedFormatCondition = {
            conditionConfig: {
                condition: {
                    type: 'yes_no',
                    value: 'да'
                },
                successAction: {
                    goto: 'continue'
                },
                successFlowId: 'continue'  // This should cause validation to fail
            }
        };

        try {
            const response6 = await axios.post(`${API_BASE_URL}/flow/validate-condition`, mixedFormatCondition);
            console.log('❌ Mixed format should fail but passed:', response6.data);
        } catch (error) {
            console.log('✅ Mixed format correctly rejected:', error.response?.data?.error || error.message);
        }

        console.log('\n🎯 Summary:');
        console.log('✅ API validation supports new format with successAction/failAction');
        console.log('✅ API validation supports always_true condition type');
        console.log('✅ API validation supports all goto patterns: continue, fin, #step, flowId, flowId#step');
        console.log('✅ API validation maintains backward compatibility with old format');
        console.log('✅ API validation correctly rejects invalid configurations');

    } catch (error) {
        console.error('💥 Test crashed:', error.message);
    }
}

testValidation().then(() => {
    console.log('\n🏁 API validation test completed');
    process.exit(0);
}).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
});
