// Тест для проверки функций валидации
import { 
    validateConditionConfig, 
    validateGoto, 
    validateAction, 
    validateCondition 
} from './utils/flowValidation.js';

async function testValidationFunctions() {
    console.log('🧪 Testing validation functions...');
    
    // Test 1: validateGoto function
    console.log('\n📋 Test 1: validateGoto function');
    const gotoTests = [
        { value: 'continue', expected: true },
        { value: 'fin', expected: true },
        { value: '#5', expected: true },
        { value: 'flow-id', expected: true },
        { value: 'flow-id#3', expected: true },
        { value: '', expected: false },
        { value: 'invalid#', expected: false },
        { value: '#invalid', expected: false },
        { value: 'flow id with spaces', expected: false }
    ];

    for (const test of gotoTests) {
        const result = validateGoto(test.value);
        const status = result.isValid === test.expected ? '✅' : '❌';
        console.log(`${status} goto: "${test.value}" - ${result.isValid ? 'valid' : 'invalid'}`);
        if (!result.isValid) {
            console.log(`   Error: ${result.error}`);
        }
    }

    // Test 2: validateAction function
    console.log('\n📋 Test 2: validateAction function');
    const actionTests = [
        { action: { goto: 'continue' }, expected: true },
        { action: { goto: 'fin' }, expected: true },
        { action: { goto: '#8' }, expected: true },
        { action: {}, expected: false },
        { action: { goto: '' }, expected: false },
        { action: null, expected: false }
    ];

    for (const test of actionTests) {
        const result = validateAction(test.action, 'testAction');
        const status = result.isValid === test.expected ? '✅' : '❌';
        console.log(`${status} action: ${JSON.stringify(test.action)} - ${result.isValid ? 'valid' : 'invalid'}`);
        if (!result.isValid) {
            console.log(`   Error: ${result.error}`);
        }
    }

    // Test 3: validateCondition function
    console.log('\n📋 Test 3: validateCondition function');
    const conditionTests = [
        { condition: { type: 'yes_no', value: 'да' }, expected: true },
        { condition: { type: 'age_check', operator: '>=', value: 18 }, expected: true },
        { condition: { type: 'always_true' }, expected: true },
        { condition: { type: 'numeric_compare', operator: '>', value: 100 }, expected: true },
        { condition: { type: 'equals', value: 'test' }, expected: true },
        { condition: { type: 'contains', value: 'test' }, expected: true },
        { condition: { type: 'invalid_type', value: 'test' }, expected: false },
        { condition: { type: 'age_check', value: 18 }, expected: false }, // missing operator
        { condition: { type: 'yes_no', value: 'maybe' }, expected: false }, // invalid yes_no value
        { condition: { type: 'age_check', operator: '>=', value: 'not_a_number' }, expected: false }
    ];

    for (const test of conditionTests) {
        const result = validateCondition(test.condition);
        const status = result.isValid === test.expected ? '✅' : '❌';
        console.log(`${status} condition: ${JSON.stringify(test.condition)} - ${result.isValid ? 'valid' : 'invalid'}`);
        if (!result.isValid) {
            console.log(`   Error: ${result.error}`);
        }
    }

    // Test 4: validateConditionConfig function - new format
    console.log('\n📋 Test 4: validateConditionConfig function - new format');
    const newFormatTests = [
        {
            config: {
                condition: { type: 'yes_no', value: 'да' },
                successAction: { goto: 'continue' },
                failAction: { goto: '#8' }
            },
            expected: true
        },
        {
            config: {
                condition: { type: 'always_true' },
                successAction: { goto: 'fin' },
                failAction: { goto: 'continue' }
            },
            expected: true
        },
        {
            config: {
                condition: { type: 'yes_no', value: 'да' },
                successAction: { goto: 'continue' }
                // missing failAction
            },
            expected: false
        },
        {
            config: {
                condition: { type: 'yes_no', value: 'да' },
                successAction: { goto: 'continue' },
                failAction: { goto: 'invalid#' }
            },
            expected: false
        }
    ];

    for (const test of newFormatTests) {
        const result = validateConditionConfig(test.config);
        const status = result.isValid === test.expected ? '✅' : '❌';
        console.log(`${status} new format config - ${result.isValid ? 'valid' : 'invalid'}`);
        if (!result.isValid) {
            console.log(`   Error: ${result.error}`);
        }
    }

    // Test 5: validateConditionConfig function - old format
    console.log('\n📋 Test 5: validateConditionConfig function - old format');
    const oldFormatTests = [
        {
            config: {
                condition: { type: 'age_check', operator: '<', value: 18 },
                successFlowId: 'continue',
                failFlowId: 'continue'
            },
            expected: true
        },
        {
            config: {
                condition: { type: 'yes_no', value: 'да' },
                successFlowId: 'some-flow-id'
            },
            expected: true
        }
    ];

    for (const test of oldFormatTests) {
        const result = validateConditionConfig(test.config);
        const status = result.isValid === test.expected ? '✅' : '❌';
        console.log(`${status} old format config - ${result.isValid ? 'valid' : 'invalid'}`);
        if (!result.isValid) {
            console.log(`   Error: ${result.error}`);
        }
    }

    // Test 6: Mixed format (should fail)
    console.log('\n📋 Test 6: Mixed format (should fail)');
    const mixedFormatConfig = {
        condition: { type: 'yes_no', value: 'да' },
        successAction: { goto: 'continue' },
        successFlowId: 'continue'  // This should cause validation to fail
    };

    const mixedResult = validateConditionConfig(mixedFormatConfig);
    const mixedStatus = !mixedResult.isValid ? '✅' : '❌';
    console.log(`${mixedStatus} mixed format correctly rejected - ${mixedResult.isValid ? 'valid' : 'invalid'}`);
    if (!mixedResult.isValid) {
        console.log(`   Error: ${mixedResult.error}`);
    }

    console.log('\n🎯 Summary:');
    console.log('✅ validateGoto supports all patterns: continue, fin, #step, flowId, flowId#step');
    console.log('✅ validateAction validates action objects with goto field');
    console.log('✅ validateCondition supports all condition types including always_true');
    console.log('✅ validateConditionConfig supports new format with successAction/failAction');
    console.log('✅ validateConditionConfig maintains backward compatibility with old format');
    console.log('✅ validateConditionConfig correctly rejects mixed formats');
}

testValidationFunctions().then(() => {
    console.log('\n🏁 Validation functions test completed');
    process.exit(0);
}).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
});
