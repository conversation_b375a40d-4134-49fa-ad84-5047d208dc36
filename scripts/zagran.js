import {Flow, FlowMessage} from '../models/index.js';
import {v4 as uuidv4} from 'uuid';
import logger from '../utils/logger.js';

async function seedFlows() {
    try {
        async function createFlowWithMessages(flowData, messages) {
            const flow = await Flow.create({ id: uuidv4(), ...flowData });
            for (const messageData of messages) {
                await FlowMessage.create({ id: uuidv4(), flowId: flow.id, ...messageData });
            }
            return flow;
        }

        // --- Терминальный поток для несовершеннолетних ---
        const minorFlow = await createFlowWithMessages({
            name: '[Терминал] Несовершеннолетний заявитель',
            isStartingFlow: false,
            aiPrompt: 'pass' // Оценка не требуется, диалог завершен.
        },[
            { content: 'Данная процедура предназначена для совершеннолетних граждан. Для оформления паспорта несовершеннолетнему обратитесь к консультанту за списком документов для законного представителя.', step: 0}
        ]);

        // --- Основной, единый и правильный поток ---
        await createFlowWithMessages({
            name: 'Оформление загранпаспорта на 5 лет (совершеннолетний)',
            description: 'Полная проверка комплекта документов в едином логическом потоке.',
            isStartingFlow: true,
            aiPrompt: `Проанализируй диалог.
1.  **Главное условие**: Возраст пользователя (step 0) должен быть 18 или больше. Если меньше, сообщить, что нужно прийти с опекуном.
2.  **Обязательные для всех**: Паспорт РФ (step 2), 3 фото (step 3), сведения о работе (step 4).
3.  **Условная логика**:
    - Если на вопрос "Военнослужащий?" (step 7) ответ 'да', то на вопросе о справке (step 8) ответ тоже должен быть 'да'.
    - Если на вопрос "Мужчина 18-30 лет?" (step 9) ответ 'да', то на вопросе о военном билете (step 10) ответ тоже должен быть 'да'.
    - Если на вопрос "Смена ФИО?" (step 11) ответ 'да', то на вопросе о документе-основании (step 12) ответ тоже должен быть 'да'.
Проверь, что все применимые к пользователю условия выполнены. Если да - 'pass', иначе - укажи, чего не хватает.`,
            successMessage: 'Отлично! Все необходимые сведения и документы у вас на руках. Можете приступать к заполнению заявления.',
            failureMessage: 'К сожалению, у вас не хватает некоторых документов или сведений. Пожалуйста, подготовьте их и попробуйте снова.'
        }, [
            {
                content: 'Здравствуйте! Я помогу вам проверить комплект документов для оформления загранпаспорта. Укажите ваш возраст (полных лет). {numeric}',
                step: 0,
                messageType: 'conditional_switch',
                conditionConfig: {
                    condition: { type: 'age_check', operator: '<', value: 18 },
                    successAction: { goto: 'fin' }, // Если младше 18 - уходим в терминальный поток
                    failAction:    { goto: 'continue' }    // Если 18+ - продолжаем
                }
            },

            // --- ОБЩИЙ БЛОК (с новой нумерацией) ---
            { content: 'У вас есть действующий паспорт гражданина РФ? {да|нет}', step: 1 },
            { content: 'Вы подготовили не менее 3 фотографий? {да|нет}', step: 2 },
            { content: 'Вы подготовили сведения о вашей трудовой/учебной деятельности за последние 10 лет? {да|нет}', step: 3 },
            { content: 'У вас есть действующий заграничный паспорт для сверки данных при заполнении заявления? {да|нет}', step: 4 },
            { content: 'Вы подготовили сведения о регистрации по месту пребывания (если она у вас есть)? {да|нет}', step: 5 },
            {
                content: 'Вы являетесь военнослужащим (за исключением службы по призыву)? {да|нет}',
                step: 6,
                messageType: 'conditional_switch',
                conditionConfig: {
                    condition: { type: 'yes_no', value: 'да' },
                    successAction: { goto: 'continue' },
                    failAction:    { goto: '#8' }
                }
            },
            {
                content: 'Вам необходимо предоставить разрешение командования. У вас есть эта справка? {да|нет}',
                step: 7
            },
            {
                content: 'Вы мужчина в возрасте от 18 до 30 лет? {да|нет}',
                step: 8,
                messageType: 'conditional_switch',
                conditionConfig: {
                    condition: { type: 'yes_no', value: 'да' },
                    successAction: { goto: 'continue' },
                    failAction:    { goto: '#10' }
                }
            },
            {
                content: 'Вам необходимо предоставить военный билет с соответствующими отметками. Он у вас есть? {да|нет}',
                step: 9
            },

            // --- ВЕТКА 3: СМЕНА ФИО (с новой нумерацией) ---
            {
                content: 'Требуется ли изменение написания вашей фамилии и/или имени? {да|нет}',
                step: 10,
                messageType: 'conditional_switch',
                conditionConfig: {
                    condition: { type: 'yes_no', value: 'да' },
                    successAction: { goto: 'continue' },
                    failAction:    { goto: 'fin' }
                }
            },
            {
                content: 'Вам понадобится документ-основание (свидетельство о браке и т.д.). У вас есть такой документ? {да|нет}',
                step: 11
            }
        ]);

        logger.info('Final, correct, and complete flow seeding has been successfully completed.');
    } catch (error) {
        logger.error(`Flow seeding failed: ${error.message}`);
        throw error;
    }
}

seedFlows().then(() => {
    logger.info('Flow seeding script finished.');
}).catch((error) => {
    logger.error('Flow seeding script failed:', error);
    process.exit(1);
});