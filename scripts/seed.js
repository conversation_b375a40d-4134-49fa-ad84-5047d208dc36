import {sequelize} from '../db.js';
import logger from '../utils/logger.js';
import './models/index.js'; // Импорт всех моделей для инициализации

async function runSeeders() {
    try {
        logger.info('Starting database seeding...');

        // Синхронизация моделей с базой данных
        await sequelize.sync({force: true});
        logger.info('Database synchronized');

        logger.info('Database seeding completed successfully');
        process.exit(0);
    } catch (error) {
        logger.error(`Database seeding failed: ${error.message}`);
        logger.error(error.stack);
        process.exit(1);
    }
}

// Запуск сидеров
runSeeders();