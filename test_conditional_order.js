// Тест для проверки правильного порядка обработки conditional_switch
import navigationService from './services/navigationService.js';

console.log('🧪 Testing navigation service with new goto: "fin" support...');

// Test 1: goto: 'fin'
try {
    const finAction = navigationService.parseGoto('fin');
    console.log('✅ Test 1 passed - goto: "fin" parsed correctly:', finAction);
    console.log('   Expected: { type: "finish", flowId: null, step: null }');
    console.log('   Actual:  ', finAction);
} catch (error) {
    console.log('❌ Test 1 failed:', error.message);
}

// Test 2: goto: 'continue' (should still work)
try {
    const continueAction = navigationService.parseGoto('continue');
    console.log('✅ Test 2 passed - goto: "continue" still works:', continueAction);
} catch (error) {
    console.log('❌ Test 2 failed:', error.message);
}

// Test 3: goto: '#9' (should still work)
try {
    const stepAction = navigationService.parseGoto('#9');
    console.log('✅ Test 3 passed - goto: "#9" still works:', stepAction);
} catch (error) {
    console.log('❌ Test 3 failed:', error.message);
}

console.log('\n🎯 Summary:');
console.log('1. Добавлена поддержка goto: "fin" для немедленного завершения диалога');
console.log('2. Исправлен порядок обработки conditional_switch:');
console.log('   - Сначала отправляется сообщение пользователю');
console.log('   - Затем при получении ответа проверяется условие');
console.log('3. Все существующие goto значения продолжают работать');

console.log('\n📋 Пример использования:');
console.log(`{
    content: 'Вы являетесь военнослужащим? {да|нет}',
    step: 7,
    messageType: 'conditional_switch',
    conditionConfig: {
        condition: { type: 'yes_no', value: 'да' },
        successAction: { goto: 'continue' },  // продолжить поток
        failAction: { goto: '#9' }            // перейти к шагу 9
    }
}`);

console.log('\nИли для немедленного завершения:');
console.log(`{
    content: 'Проверка завершена',
    step: 10,
    messageType: 'conditional_switch',
    conditionConfig: {
        condition: { type: 'always_true' },
        successAction: { goto: 'fin' },       // завершить диалог
        failAction: { goto: 'continue' }
    }
}`);
