<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Swagger UI</title>
    <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist/swagger-ui.css">
</head>
<body>
<div id="swagger-ui"></div>
<script src="https://unpkg.com/swagger-ui-dist/swagger-ui-bundle.js"></script>
<script>
    const spec = {
        "openapi": "3.0.0",
        "info": {
            "title": "UslugiBot API",
            "description": "API коллекция для UslugiBot с поддержкой условного переключения потоков",
            "version": "2.0.0"
        },
        "servers": [
            {
                "url": "http://localhost:3000",
                "description": "Локальный сервер разработки"
            },
            {
                "url": "http://*************:5555",
                "description": "Удаленный сервер разработки"
            },
            {
                "url": "http://*************:3000",
                "description": "Удаленный сервер"
            }
        ],
        "paths": {
            "/api": {
                "get": {
                    "tags": ["Проверка работоспособности"],
                    "summary": "Проверка работоспособности API",
                    "description": "Проверяет, работает ли API",
                    "responses": {
                        "200": {
                            "description": "API работает",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "message": {
                                                "type": "string",
                                                "example": "API is running"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/health": {
                "get": {
                    "tags": ["Проверка работоспособности"],
                    "summary": "Проверка состояния сервера",
                    "description": "Проверяет состояние работоспособности сервера",
                    "responses": {
                        "200": {
                            "description": "Сервер работает нормально"
                        }
                    }
                }
            },
            "/api/dialog/start": {
                "post": {
                    "tags": ["Диалог"],
                    "summary": "Начать новый диалог",
                    "description": "Создает новый диалог и возвращает его ID",
                    "responses": {
                        "201": {
                            "description": "Диалог успешно создан",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "dialogId": {
                                                "type": "string",
                                                "format": "uuid",
                                                "example": "123e4567-e89b-12d3-a456-************"
                                            },
                                            "message": {
                                                "type": "string",
                                                "example": "Здравствуйте! Я виртуальный помощник МФЦ. Чем я могу вам помочь?"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "500": {
                            "description": "Ошибка при создании диалога"
                        }
                    }
                }
            },
            "/api/dialog/{dialogId}/message": {
                "post": {
                    "tags": ["Диалог"],
                    "summary": "Обработать сообщение пользователя",
                    "description": "Обрабатывает сообщение пользователя в рамках диалога",
                    "parameters": [
                        {
                            "name": "dialogId",
                            "in": "path",
                            "required": true,
                            "description": "ID диалога",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        }
                    ],
                    "requestBody": {
                        "required": true,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "required": ["message"],
                                    "properties": {
                                        "message": {
                                            "type": "string",
                                            "example": "Здравствуйте, мне нужна помощь с документами"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Сообщение успешно обработано",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "message": {
                                                "type": "string",
                                                "example": "Конечно, я помогу вам с документами. Какие именно документы вас интересуют?"
                                            },
                                            "flowSwitched": {
                                                "type": "boolean",
                                                "example": false,
                                                "description": "Указывает, был ли выполнен переход к другому потоку"
                                            },
                                            "newFlowId": {
                                                "type": "string",
                                                "format": "uuid",
                                                "example": "123e4567-e89b-12d3-a456-************",
                                                "description": "ID нового потока (если произошло переключение)"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Диалог не найден"
                        },
                        "500": {
                            "description": "Ошибка при обработке сообщения"
                        }
                    }
                }
            },
            "/api/dialog/{dialogId}": {
                "get": {
                    "tags": ["Диалог"],
                    "summary": "Получить диалог",
                    "description": "Получить диалог по ID со всеми сообщениями",
                    "parameters": [
                        {
                            "name": "dialogId",
                            "in": "path",
                            "required": true,
                            "description": "ID диалога",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Диалог успешно получен",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Dialog"
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Диалог не найден"
                        },
                        "500": {
                            "description": "Ошибка при получении диалога"
                        }
                    }
                }
            },
            "/api/dialog/{dialogId}/messages": {
                "get": {
                    "tags": ["Диалог"],
                    "summary": "Получить сообщения диалога",
                    "description": "Получить все сообщения из диалога",
                    "parameters": [
                        {
                            "name": "dialogId",
                            "in": "path",
                            "required": true,
                            "description": "ID диалога",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Сообщения успешно получены",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": {
                                            "$ref": "#/components/schemas/Message"
                                        }
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Диалог не найден"
                        },
                        "500": {
                            "description": "Ошибка при получении сообщений"
                        }
                    }
                }
            },
            "/api/dialog/message": {
                "post": {
                    "tags": ["Диалог"],
                    "summary": "Обработать сообщение пользователя или начать новый диалог",
                    "description": "Обрабатывает сообщение пользователя в рамках существующего диалога или создает новый диалог",
                    "requestBody": {
                        "required": true,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "required": ["message"],
                                    "properties": {
                                        "dialogId": {
                                            "type": "string",
                                            "format": "uuid",
                                            "example": "123e4567-e89b-12d3-a456-************",
                                            "description": "ID диалога (если не указан, будет создан новый диалог)"
                                        },
                                        "message": {
                                            "type": "string",
                                            "example": "Здравствуйте, мне нужна помощь с документами"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Сообщение успешно обработано",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "dialogId": {
                                                "type": "string",
                                                "format": "uuid",
                                                "example": "123e4567-e89b-12d3-a456-************",
                                                "description": "ID диалога (возвращается только при создании нового диалога)"
                                            },
                                            "isNewDialog": {
                                                "type": "boolean",
                                                "example": true,
                                                "description": "Флаг, указывающий, что был создан новый диалог"
                                            },
                                            "message": {
                                                "type": "string",
                                                "example": "Конечно, я помогу вам с документами. Какие именно документы вас интересуют?"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "400": {
                            "description": "Неверные данные запроса"
                        },
                        "404": {
                            "description": "Диалог не найден"
                        },
                        "500": {
                            "description": "Ошибка при обработке сообщения"
                        }
                    }
                }
            },
            "/api/flow": {
                "post": {
                    "tags": ["Поток"],
                    "summary": "Создать поток",
                    "description": "Создать новый поток для обработки диалогов",
                    "requestBody": {
                        "required": true,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "required": ["name"],
                                    "properties": {
                                        "name": {
                                            "type": "string",
                                            "example": "Проверка документов"
                                        },
                                        "description": {
                                            "type": "string",
                                            "example": "Поток для проверки документов пользователя"
                                        },
                                        "aiPrompt": {
                                            "type": "string",
                                            "example": "Оцените, есть ли у пользователя все необходимые документы"
                                        },
                                        "successMessage": {
                                            "type": "string",
                                            "example": "Все документы проверены"
                                        },
                                        "failureMessage": {
                                            "type": "string",
                                            "example": "Некоторые документы отсутствуют"
                                        },
                                        "isStartingFlow": {
                                            "type": "boolean",
                                            "example": true,
                                            "description": "Определяет, может ли поток использоваться как начальный"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "201": {
                            "description": "Поток успешно создан",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "flowId": {
                                                "type": "string",
                                                "format": "uuid",
                                                "example": "123e4567-e89b-12d3-a456-************"
                                            },
                                            "message": {
                                                "type": "string",
                                                "example": "Поток успешно создан"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "400": {
                            "description": "Неверные данные запроса"
                        },
                        "500": {
                            "description": "Ошибка при создании потока"
                        }
                    }
                },
                "get": {
                    "tags": ["Поток"],
                    "summary": "Получить все потоки",
                    "description": "Получить список всех потоков",
                    "responses": {
                        "200": {
                            "description": "Потоки успешно получены",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": {
                                            "$ref": "#/components/schemas/Flow"
                                        }
                                    }
                                }
                            }
                        },
                        "500": {
                            "description": "Ошибка при получении потоков"
                        }
                    }
                }
            },
            "/api/flow/{flowId}": {
                "get": {
                    "tags": ["Поток"],
                    "summary": "Получить поток",
                    "description": "Получить поток по ID с его сообщениями",
                    "parameters": [
                        {
                            "name": "flowId",
                            "in": "path",
                            "required": true,
                            "description": "ID потока",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Поток успешно получен",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Flow"
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Поток не найден"
                        },
                        "500": {
                            "description": "Ошибка при получении потока"
                        }
                    }
                },
                "put": {
                    "tags": ["Поток"],
                    "summary": "Обновить поток",
                    "description": "Обновить существующий поток",
                    "parameters": [
                        {
                            "name": "flowId",
                            "in": "path",
                            "required": true,
                            "description": "ID потока",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        }
                    ],
                    "requestBody": {
                        "required": true,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "name": {
                                            "type": "string",
                                            "example": "Обновленный поток проверки документов"
                                        },
                                        "description": {
                                            "type": "string",
                                            "example": "Обновленный поток для проверки документов пользователя"
                                        },
                                        "aiPrompt": {
                                            "type": "string",
                                            "example": "Оцените, есть ли у пользователя все необходимые документы"
                                        },
                                        "successMessage": {
                                            "type": "string",
                                            "example": "Все документы проверены"
                                        },
                                        "failureMessage": {
                                            "type": "string",
                                            "example": "Некоторые документы отсутствуют"
                                        },
                                        "isStartingFlow": {
                                            "type": "boolean",
                                            "example": true,
                                            "description": "Определяет, может ли поток использоваться как начальный"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Поток успешно обновлен",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Flow"
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Поток не найден"
                        },
                        "500": {
                            "description": "Ошибка при обновлении потока"
                        }
                    }
                },
                "delete": {
                    "tags": ["Поток"],
                    "summary": "Удалить поток",
                    "description": "Удалить существующий поток",
                    "parameters": [
                        {
                            "name": "flowId",
                            "in": "path",
                            "required": true,
                            "description": "ID потока",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Поток успешно удален"
                        },
                        "404": {
                            "description": "Поток не найден"
                        },
                        "500": {
                            "description": "Ошибка при удалении потока"
                        }
                    }
                }
            },
            "/api/flow/{flowId}/message": {
                "post": {
                    "tags": ["Поток"],
                    "summary": "Добавить сообщение в поток",
                    "description": "Добавить новое сообщение в поток",
                    "parameters": [
                        {
                            "name": "flowId",
                            "in": "path",
                            "required": true,
                            "description": "ID потока",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        }
                    ],
                    "requestBody": {
                        "required": true,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "required": ["content", "step"],
                                    "properties": {
                                        "content": {
                                            "type": "string",
                                            "example": "У вас есть паспорт?"
                                        },
                                        "step": {
                                            "type": "integer",
                                            "example": 1
                                        },
                                        "messageType": {
                                            "type": "string",
                                            "enum": ["regular", "conditional_switch"],
                                            "example": "regular",
                                            "description": "Тип сообщения: обычное или условное переключение"
                                        },
                                        "conditionConfig": {
                                            "$ref": "#/components/schemas/ConditionConfig",
                                            "description": "Конфигурация условий (только для conditional_switch)"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "201": {
                            "description": "Сообщение успешно добавлено",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/FlowMessage"
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Поток не найден"
                        },
                        "500": {
                            "description": "Ошибка при добавлении сообщения"
                        }
                    }
                }
            },
            "/api/flow/{flowId}/message/{messageId}": {
                "put": {
                    "tags": ["Поток"],
                    "summary": "Обновить сообщение в потоке",
                    "description": "Обновить существующее сообщение в потоке",
                    "parameters": [
                        {
                            "name": "flowId",
                            "in": "path",
                            "required": true,
                            "description": "ID потока",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        },
                        {
                            "name": "messageId",
                            "in": "path",
                            "required": true,
                            "description": "ID сообщения",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        }
                    ],
                    "requestBody": {
                        "required": true,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "string",
                                            "example": "Обновленное содержание сообщения"
                                        },
                                        "step": {
                                            "type": "integer",
                                            "example": 2
                                        },
                                        "messageType": {
                                            "type": "string",
                                            "enum": ["regular", "conditional_switch"],
                                            "example": "regular",
                                            "description": "Тип сообщения: обычное или условное переключение"
                                        },
                                        "conditionConfig": {
                                            "$ref": "#/components/schemas/ConditionConfig",
                                            "description": "Конфигурация условий (только для conditional_switch)"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Сообщение успешно обновлено",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/FlowMessage"
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Поток или сообщение не найдены"
                        },
                        "500": {
                            "description": "Ошибка при обновлении сообщения"
                        }
                    }
                },
                "delete": {
                    "tags": ["Поток"],
                    "summary": "Удалить сообщение из потока",
                    "description": "Удалить существующее сообщение из потока",
                    "parameters": [
                        {
                            "name": "flowId",
                            "in": "path",
                            "required": true,
                            "description": "ID потока",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        },
                        {
                            "name": "messageId",
                            "in": "path",
                            "required": true,
                            "description": "ID сообщения",
                            "schema": {
                                "type": "string",
                                "format": "uuid"
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Сообщение успешно удалено"
                        },
                        "404": {
                            "description": "Поток или сообщение не найдены"
                        },
                        "500": {
                            "description": "Ошибка при удалении сообщения"
                        }
                    }
                }
            },
            "/api/flow/starting-flows": {
                "get": {
                    "tags": ["Поток"],
                    "summary": "Получить начальные потоки",
                    "description": "Получить список потоков, которые могут использоваться как начальные",
                    "responses": {
                        "200": {
                            "description": "Начальные потоки успешно получены",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": {
                                            "$ref": "#/components/schemas/Flow"
                                        }
                                    }
                                }
                            }
                        },
                        "500": {
                            "description": "Ошибка при получении потоков"
                        }
                    }
                }
            },
            "/api/flow/validate-condition": {
                "post": {
                    "tags": ["Поток"],
                    "summary": "Валидировать конфигурацию условий",
                    "description": "Проверить корректность конфигурации условий без создания сообщения",
                    "requestBody": {
                        "required": true,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "required": ["conditionConfig"],
                                    "properties": {
                                        "conditionConfig": {
                                            "$ref": "#/components/schemas/ConditionConfig"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Конфигурация условий валидна",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "valid": {
                                                "type": "boolean",
                                                "example": true
                                            },
                                            "message": {
                                                "type": "string",
                                                "example": "Конфигурация условий корректна"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "400": {
                            "description": "Конфигурация условий невалидна",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "valid": {
                                                "type": "boolean",
                                                "example": false
                                            },
                                            "error": {
                                                "type": "string",
                                                "example": "Тип условия должен быть одним из: age_check, yes_no, equals, contains, numeric_compare, always_true"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "500": {
                            "description": "Ошибка при валидации условий"
                        }
                    }
                }
            }
        },
        "components": {
            "schemas": {
                "Dialog": {
                    "type": "object",
                    "properties": {
                        "id": {
                            "type": "string",
                            "format": "uuid",
                            "example": "123e4567-e89b-12d3-a456-************"
                        },
                        "flowId": {
                            "type": "string",
                            "format": "uuid",
                            "nullable": true,
                            "example": "123e4567-e89b-12d3-a456-************"
                        },
                        "currentStep": {
                            "type": "integer",
                            "example": 0
                        },
                        "collectedData": {
                            "type": "object",
                            "example": {}
                        },
                        "completed": {
                            "type": "boolean",
                            "example": false
                        },
                        "messages": {
                            "type": "array",
                            "items": {
                                "$ref": "#/components/schemas/Message"
                            }
                        }
                    }
                },
                "Message": {
                    "type": "object",
                    "properties": {
                        "id": {
                            "type": "string",
                            "format": "uuid",
                            "example": "123e4567-e89b-12d3-a456-************"
                        },
                        "dialogId": {
                            "type": "string",
                            "format": "uuid",
                            "example": "123e4567-e89b-12d3-a456-************"
                        },
                        "role": {
                            "type": "string",
                            "enum": ["user", "assistant"],
                            "example": "assistant"
                        },
                        "content": {
                            "type": "string",
                            "example": "Здравствуйте! Чем я могу вам помочь?"
                        },
                        "timestamp": {
                            "type": "string",
                            "format": "date-time",
                            "example": "2023-01-01T12:00:00Z"
                        }
                    }
                },
                "Flow": {
                    "type": "object",
                    "properties": {
                        "id": {
                            "type": "string",
                            "format": "uuid",
                            "example": "123e4567-e89b-12d3-a456-************"
                        },
                        "name": {
                            "type": "string",
                            "example": "Проверка документов"
                        },
                        "description": {
                            "type": "string",
                            "nullable": true,
                            "example": "Поток для проверки документов пользователя"
                        },
                        "aiPrompt": {
                            "type": "string",
                            "nullable": true,
                            "example": "Оцените, есть ли у пользователя все необходимые документы"
                        },
                        "successMessage": {
                            "type": "string",
                            "nullable": true,
                            "example": "Все документы проверены"
                        },
                        "failureMessage": {
                            "type": "string",
                            "nullable": true,
                            "example": "Некоторые документы отсутствуют"
                        },
                        "isStartingFlow": {
                            "type": "boolean",
                            "example": true,
                            "description": "Определяет, может ли поток использоваться как начальный"
                        },
                        "messages": {
                            "type": "array",
                            "items": {
                                "$ref": "#/components/schemas/FlowMessage"
                            }
                        }
                    }
                },
                "FlowMessage": {
                    "type": "object",
                    "properties": {
                        "id": {
                            "type": "string",
                            "format": "uuid",
                            "example": "123e4567-e89b-12d3-a456-************"
                        },
                        "flowId": {
                            "type": "string",
                            "format": "uuid",
                            "example": "123e4567-e89b-12d3-a456-************"
                        },
                        "content": {
                            "type": "string",
                            "example": "У вас есть паспорт?"
                        },
                        "step": {
                            "type": "integer",
                            "example": 1
                        },
                        "messageType": {
                            "type": "string",
                            "enum": ["regular", "conditional_switch"],
                            "example": "regular",
                            "description": "Тип сообщения: обычное или условное переключение"
                        },
                        "conditionConfig": {
                            "$ref": "#/components/schemas/ConditionConfig",
                            "description": "Конфигурация условий (только для conditional_switch)"
                        }
                    }
                },
                "ConditionConfig": {
                    "type": "object",
                    "description": "Конфигурация условий для переключения потоков. Поддерживает два формата: новый (с successAction/failAction) и старый (с successFlowId/failFlowId) для обратной совместимости.",
                    "required": ["condition"],
                    "properties": {
                        "condition": {
                            "$ref": "#/components/schemas/Condition",
                            "description": "Условие для проверки"
                        },
                        "successAction": {
                            "$ref": "#/components/schemas/NavigationAction",
                            "description": "Действие при выполнении условия (новый формат)"
                        },
                        "failAction": {
                            "$ref": "#/components/schemas/NavigationAction",
                            "description": "Действие при невыполнении условия (новый формат)"
                        },
                        "successFlowId": {
                            "type": "string",
                            "example": "123e4567-e89b-12d3-a456-************",
                            "description": "ID потока для перехода при выполнении условия, 'continue' для продолжения текущего потока (старый формат, для обратной совместимости)"
                        },
                        "failFlowId": {
                            "type": "string",
                            "example": "123e4567-e89b-12d3-a456-************",
                            "description": "ID потока для перехода при невыполнении условия, 'continue' для продолжения текущего потока (старый формат, для обратной совместимости)"
                        }
                    },
                    "examples": [
                        {
                            "name": "Новый формат с навигацией",
                            "value": {
                                "condition": {
                                    "type": "yes_no",
                                    "value": "да"
                                },
                                "successAction": {
                                    "goto": "continue"
                                },
                                "failAction": {
                                    "goto": "#8"
                                }
                            }
                        },
                        {
                            "name": "Старый формат (обратная совместимость)",
                            "value": {
                                "condition": {
                                    "type": "age_check",
                                    "operator": "<",
                                    "value": 18
                                },
                                "successFlowId": "minor-flow-id",
                                "failFlowId": "continue"
                            }
                        }
                    ]
                },
                "NavigationAction": {
                    "type": "object",
                    "description": "Действие навигации для условного переключения",
                    "required": ["goto"],
                    "properties": {
                        "goto": {
                            "type": "string",
                            "description": "Направление навигации",
                            "enum": ["continue", "fin"],
                            "pattern": "^(continue|fin|#\\d+|[a-zA-Z0-9-_]+|[a-zA-Z0-9-_]+#\\d+)$",
                            "examples": [
                                "continue",
                                "fin",
                                "#8",
                                "other-flow-id",
                                "other-flow-id#5"
                            ]
                        }
                    },
                    "examples": [
                        {
                            "name": "Продолжить текущий поток",
                            "value": {"goto": "continue"}
                        },
                        {
                            "name": "Завершить диалог",
                            "value": {"goto": "fin"}
                        },
                        {
                            "name": "Перейти к шагу 8",
                            "value": {"goto": "#8"}
                        },
                        {
                            "name": "Перейти к другому потоку",
                            "value": {"goto": "other-flow-id"}
                        },
                        {
                            "name": "Перейти к шагу 5 в другом потоке",
                            "value": {"goto": "other-flow-id#5"}
                        }
                    ]
                },
                "Condition": {
                    "type": "object",
                    "description": "Условие для проверки текущего ответа пользователя",
                    "required": ["type"],
                    "properties": {
                        "type": {
                            "type": "string",
                            "enum": ["age_check", "yes_no", "equals", "contains", "numeric_compare", "always_true"],
                            "example": "age_check",
                            "description": "Тип условия"
                        },
                        "operator": {
                            "type": "string",
                            "enum": [">", "<", ">=", "<=", "==", "=", "!="],
                            "example": ">=",
                            "description": "Оператор сравнения (обязательно для age_check и numeric_compare)"
                        },
                        "value": {
                            "oneOf": [
                                {"type": "string"},
                                {"type": "number"},
                                {"type": "boolean"}
                            ],
                            "example": 18,
                            "description": "Значение для сравнения с ответом пользователя (не требуется для always_true)"
                        }
                    },
                    "examples": [
                        {
                            "name": "Проверка возраста",
                            "value": {
                                "type": "age_check",
                                "operator": ">=",
                                "value": 18
                            }
                        },
                        {
                            "name": "Проверка да/нет",
                            "value": {
                                "type": "yes_no",
                                "value": "да"
                            }
                        },
                        {
                            "name": "Проверка равенства",
                            "value": {
                                "type": "equals",
                                "value": "паспорт"
                            }
                        },
                        {
                            "name": "Проверка содержания",
                            "value": {
                                "type": "contains",
                                "value": "документ"
                            }
                        },
                        {
                            "name": "Числовое сравнение",
                            "value": {
                                "type": "numeric_compare",
                                "operator": ">",
                                "value": 100
                            }
                        },
                        {
                            "name": "Всегда истина (безусловный переход)",
                            "value": {
                                "type": "always_true"
                            }
                        }
                    ]
                }
            }
        }
    };
    SwaggerUIBundle({
        spec: spec,
        dom_id: '#swagger-ui'
    });
</script>
</body>
</html>
