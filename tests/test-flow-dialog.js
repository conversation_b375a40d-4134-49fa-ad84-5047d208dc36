import flowDialogService from '../services/flowDialogService.js';
import {Flow, FlowMessage} from '../models/index.js';
import logger from '../utils/logger.js';

// Тестирование сервиса диалогов с потоками
async function testFlowDialogService() {
    try {
        logger.info('Starting flow dialog service test...');

        // Получаем первый поток из базы
        const flow = await Flow.findOne({
            include: [{model: FlowMessage}]
        });

        if (!flow) {
            logger.error('No flows found in database. Run seed script first.');
            return;
        }

        logger.info(`Testing flow: ${flow.name}`);

        // Начинаем диалог
        const startResult = await flowDialogService.startDialog(flow.id);
        logger.info(`Started dialog: ${JSON.stringify(startResult, null, 2)}`);

        // Отвечаем на первый вопрос
        const response1 = await flowDialogService.processMessage(startResult.dialogId, 'Да, у меня есть паспорт');
        logger.info(`Response 1: ${JSON.stringify(response1, null, 2)}`);

        // Отвечаем на второй вопрос
        const response2 = await flowDialogService.processMessage(response1.dialogId, 'Да, заявление заполнено');
        logger.info(`Response 2: ${JSON.stringify(response2, null, 2)}`);

        // Продолжаем диалог до конца
        let lastResponse = response2;
        let responseCount = 2;

        while (!lastResponse.completed) {
            responseCount++;
            // Генерируем разные ответы в зависимости от шага
            let userResponse;

            if (lastResponse.message.includes('дату')) {
                userResponse = '15.06.1990';
            } else if (lastResponse.message.includes('возраст')) {
                userResponse = '32';
            } else if (lastResponse.message.includes('впервые или')) {
                userResponse = 'впервые';
            } else if (lastResponse.message.includes('для себя или')) {
                userResponse = 'для себя';
            } else {
                userResponse = 'Да';
            }

            lastResponse = await flowDialogService.processMessage(lastResponse.dialogId, userResponse);
            logger.info(`Response ${responseCount}: ${JSON.stringify(lastResponse, null, 2)}`);
        }

        logger.info('Flow dialog test completed successfully');
    } catch (error) {
        logger.error(`Flow dialog test failed: ${error.message}`);
        logger.error(error.stack);
    }
}

// Запускаем тест через 2 секунды, чтобы дать время на инициализацию БД
setTimeout(() => {
    testFlowDialogService();
}, 2000);