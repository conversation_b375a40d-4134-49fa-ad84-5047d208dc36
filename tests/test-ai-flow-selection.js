import dialogService from '../services/dialogService.js';
import {Flow} from '../models/index.js';
import logger from '../utils/logger.js';
import {sequelize} from '../db.js';

// Тестирование выбора потока с помощью ИИ
async function testAIFlowSelection() {
    try {
        logger.info('Starting AI flow selection test...');

        // Синхронизируем модели без удаления данных
        await sequelize.sync({force: false});

        // Проверяем наличие потоков в базе
        const flowCount = await Flow.count();
        if (flowCount === 0) {
            logger.error('No flows found in database. Run seed script first.');
            return;
        }

        logger.info(`Found ${flowCount} flows in database.`);

        // Начинаем диалог
        const startResult = await dialogService.startDialog();
        logger.info(`Started dialog: ${JSON.stringify(startResult, null, 2)}`);

        // Тестовые запросы для определения потока
        const testQueries = [
            "Мне нужно получить СНИЛС",
            "Хочу развестись с мужем",
            "Нужна справка о судимости",
            "Как получить загранпаспорт?",
            "Мне нужно зарегистрировать ребенка"
        ];

        // Обрабатываем каждый запрос
        for (const query of testQueries) {
            logger.info(`Testing query: "${query}"`);

            const result = await dialogService.processMessage(startResult.dialogId, query);

            logger.info(`Result: ${JSON.stringify(result, null, 2)}`);

            // Если поток определен, проверяем его
            if (result.flowDetermined) {
                logger.info(`Flow determined: ${result.flowId}`);

                // Отправляем еще одно сообщение для проверки работы потока
                const nextResult = await dialogService.processMessage(startResult.dialogId, "Да, у меня есть все документы");
                logger.info(`Next response: ${JSON.stringify(nextResult, null, 2)}`);
            } else {
                logger.info(`Flow not determined. Explanation: ${result.message}`);
            }

            // Создаем новый диалог для следующего запроса
            if (testQueries.indexOf(query) < testQueries.length - 1) {
                const newDialog = await dialogService.startDialog();
                startResult.dialogId = newDialog.dialogId;
            }
        }

        logger.info('AI flow selection test completed successfully');
    } catch (error) {
        logger.error(`AI flow selection test failed: ${error.message}`);
        logger.error(error.stack);
    }
}

// Запускаем тест
testAIFlowSelection();