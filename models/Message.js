import {DataTypes} from 'sequelize';
import {sequelize} from '../db.js';
import Dialog from './Dialog.js';

const Message = sequelize.define('Message', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
        allowNull: false
    },
    dialogId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
            model: Dialog,
            key: 'id'
        }
    },
    role: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
            isIn: [['user', 'assistant', 'system']]
        }
    },
    content: {
        type: DataTypes.TEXT,
        allowNull: false
    },
    timestamp: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
    }
}, {
    tableName: 'messages',
    timestamps: false
});

// Define the relationship between Dialog and Message
Dialog.hasMany(Message, {
    foreignKey: 'dialogId',
    onDelete: 'CASCADE',
    hooks: true // Enable hooks for cascading delete
});
Message.belongsTo(Dialog, {
    foreignKey: 'dialogId',
    onDelete: 'CASCADE' // Also specify onDelete here
});

export default Message;
