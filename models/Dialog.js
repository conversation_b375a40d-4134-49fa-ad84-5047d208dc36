import {DataTypes} from 'sequelize';
import {sequelize} from '../db.js';
import Flow from './Flow.js';

const Dialog = sequelize.define('Dialog', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
        allowNull: false
    },
    flowId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
            model: Flow,
            key: 'id'
        }
    },
    currentStep: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0
    },
    collectedData: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {}
    },
    completed: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
    },
    completionResult: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
    }
}, {
    tableName: 'dialogs',
    timestamps: true
});

// Define relationship with Flow
Dialog.belongsTo(Flow, {
    foreignKey: 'flowId'
});

export default Dialog;
