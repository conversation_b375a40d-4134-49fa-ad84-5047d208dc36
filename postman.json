{"info": {"name": "UslugiBot API", "description": "API collection for UslugiBot", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}], "item": [{"name": "Health Check", "item": [{"name": "API Health Check", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api", "host": ["{{baseUrl}}"], "path": ["api"]}, "description": "Check if the API is running"}}, {"name": "Server Health Check", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check server health status"}}]}, {"name": "Dialog", "item": [{"name": "Start Dialog", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/api/dialog/start", "host": ["{{baseUrl}}"], "path": ["api", "dialog", "start"]}, "description": "Start a new dialog"}}, {"name": "Process Message", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/api/dialog/:dialogId/message", "host": ["{{baseUrl}}"], "path": ["api", "dialog", ":dialogId", "message"], "variable": [{"key": "dialogId", "value": "", "description": "ID of the dialog"}]}, "body": {"mode": "raw", "raw": "{\n    \"message\": \"Hello, I need help with my documents\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Process a user message in a dialog"}}, {"name": "Get Dialog", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/dialog/:dialogId", "host": ["{{baseUrl}}"], "path": ["api", "dialog", ":dialogId"], "variable": [{"key": "dialogId", "value": "", "description": "ID of the dialog"}]}, "description": "Get a dialog by ID with all messages"}}, {"name": "Get Dialog Messages", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/dialog/:dialogId/messages", "host": ["{{baseUrl}}"], "path": ["api", "dialog", ":dialogId", "messages"], "variable": [{"key": "dialogId", "value": "", "description": "ID of the dialog"}]}, "description": "Get all messages from a dialog"}}]}, {"name": "Flow", "item": [{"name": "Create Flow", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/api/flow", "host": ["{{baseUrl}}"], "path": ["api", "flow"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Document Verification Flow\",\n    \"description\": \"Flow for verifying user documents\",\n    \"aiPrompt\": \"Evaluate if the user has all required documents\",\n    \"successMessage\": \"All documents are verified\",\n    \"failureMessage\": \"Some documents are missing\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Create a new flow"}}, {"name": "Get Flow", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/flow/:flowId", "host": ["{{baseUrl}}"], "path": ["api", "flow", ":flowId"], "variable": [{"key": "flowId", "value": "", "description": "ID of the flow"}]}, "description": "Get a flow by ID with its messages"}}, {"name": "Delete Flow", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/api/flow/:flowId", "host": ["{{baseUrl}}"], "path": ["api", "flow", ":flowId"], "variable": [{"key": "flowId", "value": "", "description": "ID of the flow"}]}, "description": "Delete a flow"}}, {"name": "Add Flow Message", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/api/flow/:flowId/message", "host": ["{{baseUrl}}"], "path": ["api", "flow", ":flowId", "message"], "variable": [{"key": "flowId", "value": "", "description": "ID of the flow"}]}, "body": {"mode": "raw", "raw": "{\n    \"content\": \"Do you have your passport?\",\n    \"step\": 1\n}", "options": {"raw": {"language": "json"}}}, "description": "Add a message to a flow"}}, {"name": "Update Flow Message", "request": {"method": "PUT", "url": {"raw": "{{baseUrl}}/api/flow/:flowId/message/:messageId", "host": ["{{baseUrl}}"], "path": ["api", "flow", ":flowId", "message", ":messageId"], "variable": [{"key": "flowId", "value": "", "description": "ID of the flow"}, {"key": "messageId", "value": "", "description": "ID of the message"}]}, "body": {"mode": "raw", "raw": "{\n    \"content\": \"Updated message content\",\n    \"step\": 2\n}", "options": {"raw": {"language": "json"}}}, "description": "Update a message in a flow"}}, {"name": "Delete Flow Message", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/api/flow/:flowId/message/:messageId", "host": ["{{baseUrl}}"], "path": ["api", "flow", ":flowId", "message", ":messageId"], "variable": [{"key": "flowId", "value": "", "description": "ID of the flow"}, {"key": "messageId", "value": "", "description": "ID of the message"}]}, "description": "Delete a message from a flow"}}, {"name": "Get All Flows", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/flow", "host": ["{{baseUrl}}"], "path": ["api", "flow"]}, "description": "Get all flows"}}, {"name": "Update Flow", "request": {"method": "PUT", "url": {"raw": "{{baseUrl}}/api/flow/:flowId", "host": ["{{baseUrl}}"], "path": ["api", "flow", ":flowId"], "variable": [{"key": "flowId", "value": "", "description": "ID of the flow"}]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Document Verification Flow\",\n    \"description\": \"Updated flow for verifying user documents\",\n    \"aiPrompt\": \"Evaluate if the user has all required documents\",\n    \"successMessage\": \"All documents are verified\",\n    \"failureMessage\": \"Some documents are missing\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Update a flow"}}]}]}