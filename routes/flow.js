import express from 'express';
import {Flow, FlowMessage} from '../models/index.js';
import {v4 as uuidv4} from 'uuid';
import logger from '../utils/logger.js';
import {
    validateIsStartingFlow,
    validateFlowMessageData
} from '../utils/flowValidation.js';

const router = express.Router();

/**
 * @route POST /api/flow
 * @description Create a new flow
 * @access Public
 */
router.post('/', async (req, res) => {
    try {
        const {name, description, aiPrompt, successMessage, failureMessage, isStartingFlow} = req.body;

        if (!name) {
            return res.status(400).json({error: 'Name is required'});
        }

        // Validate isStartingFlow parameter
        const isStartingFlowValidation = validateIsStartingFlow(isStartingFlow);
        if (!isStartingFlowValidation.isValid) {
            return res.status(400).json({error: isStartingFlowValidation.error});
        }

        const flow = await Flow.create({
            id: uuidv4(),
            name,
            description: description || null,
            aiPrompt: aiPrompt || null,
            successMessage: successMessage || null,
            failureMessage: failureMessage || null,
            isStartingFlow: isStartingFlow !== undefined ? isStartingFlow : true
        });

        logger.info(`Created new flow with ID: ${flow.id}, isStartingFlow: ${flow.isStartingFlow}`);
        return res.status(201).json({
            flowId: flow.id,
            name: flow.name,
            description: flow.description,
            aiPrompt: flow.aiPrompt,
            successMessage: flow.successMessage,
            failureMessage: flow.failureMessage,
            isStartingFlow: flow.isStartingFlow,
            createdAt: flow.createdAt,
            message: 'Flow created successfully'
        });
    } catch (error) {
        logger.error(`Error creating flow: ${error.message}`);
        return res.status(500).json({error: 'Failed to create flow'});
    }
});

/**
 * @route GET /api/flow/:flowId
 * @description Get a flow by ID with its messages
 * @access Public
 */
router.get('/:flowId', async (req, res) => {
    try {
        const {flowId} = req.params;
        const flow = await Flow.findByPk(flowId, {
            include: [{
                model: FlowMessage,
                order: [['step', 'ASC']]
            }]
        });

        if (!flow) {
            return res.status(404).json({error: 'Flow not found'});
        }

        return res.status(200).json(flow);
    } catch (error) {
        logger.error(`Error getting flow: ${error.message}`);
        return res.status(500).json({error: 'Failed to get flow'});
    }
});

/**
 * @route DELETE /api/flow/:flowId
 * @description Delete a flow
 * @access Public
 */
router.delete('/:flowId', async (req, res) => {
    try {
        const {flowId} = req.params;
        const flow = await Flow.findByPk(flowId);

        if (!flow) {
            return res.status(404).json({error: 'Flow not found'});
        }

        await flow.destroy();
        logger.info(`Deleted flow: ${flowId}`);

        return res.status(200).json({
            message: 'Flow deleted successfully'
        });
    } catch (error) {
        logger.error(`Error deleting flow: ${error.message}`);
        return res.status(500).json({error: 'Failed to delete flow'});
    }
});

/**
 * @route POST /api/flow/:flowId/message
 * @description Add a message to a flow
 * @access Public
 */
router.post('/:flowId/message', async (req, res) => {
    try {
        const {flowId} = req.params;
        const {content, step, messageType, conditionConfig} = req.body;

        // Validate message data
        const messageData = {content, step, messageType, conditionConfig};
        const validation = await validateFlowMessageData(messageData, false);
        if (!validation.isValid) {
            return res.status(400).json({error: validation.error});
        }

        const flow = await Flow.findByPk(flowId);
        if (!flow) {
            return res.status(404).json({error: 'Flow not found'});
        }

        // Check if a message with this step already exists
        const existingMessage = await FlowMessage.findOne({
            where: {flowId, step}
        });

        if (existingMessage) {
            return res.status(400).json({
                error: `A message with step ${step} already exists in this flow`
            });
        }

        const message = await FlowMessage.create({
            id: uuidv4(),
            flowId,
            content,
            step,
            messageType: messageType || 'regular',
            conditionConfig: conditionConfig || null
        });

        logger.info(`Added ${messageType || 'regular'} message to flow ${flowId} at step ${step}`);
        return res.status(201).json(message);
    } catch (error) {
        logger.error(`Error adding message to flow: ${error.message}`);
        return res.status(500).json({error: 'Failed to add message to flow'});
    }
});

/**
 * @route PUT /api/flow/:flowId/message/:messageId
 * @description Update a message in a flow
 * @access Public
 */
router.put('/:flowId/message/:messageId', async (req, res) => {
    try {
        const {flowId, messageId} = req.params;
        const {content, step, messageType, conditionConfig} = req.body;

        // Validate that at least one field is provided
        if (!content && step === undefined && !messageType && !conditionConfig) {
            return res.status(400).json({
                error: 'At least one field (content, step, messageType, or conditionConfig) is required'
            });
        }

        // Validate message data
        const messageData = {content, step, messageType, conditionConfig};
        const validation = await validateFlowMessageData(messageData, true);
        if (!validation.isValid) {
            return res.status(400).json({error: validation.error});
        }

        const message = await FlowMessage.findOne({
            where: {id: messageId, flowId}
        });

        if (!message) {
            return res.status(404).json({error: 'Message not found in this flow'});
        }

        // If changing step, check if the new step is already taken
        if (step !== undefined && step !== message.step) {
            const existingMessage = await FlowMessage.findOne({
                where: {flowId, step}
            });

            if (existingMessage) {
                return res.status(400).json({
                    error: `A message with step ${step} already exists in this flow`
                });
            }
        }

        // Build update data
        const updateData = {};
        if (content) updateData.content = content;
        if (step !== undefined) updateData.step = step;
        if (messageType) updateData.messageType = messageType;
        if (conditionConfig !== undefined) updateData.conditionConfig = conditionConfig;

        // Special validation: if changing to conditional_switch, ensure conditionConfig is provided
        const finalMessageType = messageType || message.messageType;
        if (finalMessageType === 'conditional_switch' && !conditionConfig && !message.conditionConfig) {
            return res.status(400).json({
                error: 'conditionConfig is required for conditional_switch messages'
            });
        }

        // Special validation: if changing from conditional_switch to regular, clear conditionConfig
        if (messageType === 'regular' && message.messageType === 'conditional_switch') {
            updateData.conditionConfig = null;
        }

        await message.update(updateData);
        logger.info(`Updated ${finalMessageType} message ${messageId} in flow ${flowId}`);

        return res.status(200).json(await message.reload());
    } catch (error) {
        logger.error(`Error updating message: ${error.message}`);
        return res.status(500).json({error: 'Failed to update message'});
    }
});

/**
 * @route DELETE /api/flow/:flowId/message/:messageId
 * @description Delete a message from a flow
 * @access Public
 */
router.delete('/:flowId/message/:messageId', async (req, res) => {
    try {
        const {flowId, messageId} = req.params;

        const message = await FlowMessage.findOne({
            where: {id: messageId, flowId}
        });

        if (!message) {
            return res.status(404).json({error: 'Message not found in this flow'});
        }

        await message.destroy();
        logger.info(`Deleted message ${messageId} from flow ${flowId}`);

        return res.status(200).json({
            message: 'Message deleted successfully'
        });
    } catch (error) {
        logger.error(`Error deleting message: ${error.message}`);
        return res.status(500).json({error: 'Failed to delete message'});
    }
});

/**
 * @route GET /api/flow
 * @description Get all flows
 * @access Public
 */
router.get('/', async (req, res) => {
    try {
        const flows = await Flow.findAll({
            order: [['createdAt', 'DESC']]
        });

        return res.status(200).json(flows);
    } catch (error) {
        logger.error(`Error getting flows: ${error.message}`);
        return res.status(500).json({error: 'Failed to get flows'});
    }
});

/**
 * @route PUT /api/flow/:flowId
 * @description Update a flow
 * @access Public
 */
router.put('/:flowId', async (req, res) => {
    try {
        const {flowId} = req.params;
        const {name, description, aiPrompt, successMessage, failureMessage, isStartingFlow} = req.body;

        const flow = await Flow.findByPk(flowId);
        if (!flow) {
            return res.status(404).json({error: 'Flow not found'});
        }

        // Validate isStartingFlow parameter if provided
        if (isStartingFlow !== undefined) {
            const isStartingFlowValidation = validateIsStartingFlow(isStartingFlow);
            if (!isStartingFlowValidation.isValid) {
                return res.status(400).json({error: isStartingFlowValidation.error});
            }
        }

        const updateData = {};
        if (name) updateData.name = name;
        if (description !== undefined) updateData.description = description;
        if (aiPrompt !== undefined) updateData.aiPrompt = aiPrompt;
        if (successMessage !== undefined) updateData.successMessage = successMessage;
        if (failureMessage !== undefined) updateData.failureMessage = failureMessage;
        if (isStartingFlow !== undefined) updateData.isStartingFlow = isStartingFlow;

        await flow.update(updateData);
        logger.info(`Updated flow ${flowId}, isStartingFlow: ${flow.isStartingFlow}`);

        return res.status(200).json(await flow.reload());
    } catch (error) {
        logger.error(`Error updating flow: ${error.message}`);
        return res.status(500).json({error: 'Failed to update flow'});
    }
});

/**
 * @route POST /api/flow/validate-condition
 * @description Validate a condition configuration without creating a message
 * @access Public
 */
router.post('/validate-condition', async (req, res) => {
    try {
        const {conditionConfig} = req.body;

        if (!conditionConfig) {
            return res.status(400).json({error: 'conditionConfig is required'});
        }

        // Import validation functions
        const {validateConditionConfig, validateFlowReferences} = await import('../utils/flowValidation.js');

        // Validate condition configuration structure
        const configValidation = validateConditionConfig(conditionConfig);
        if (!configValidation.isValid) {
            return res.status(400).json({
                valid: false,
                error: configValidation.error
            });
        }

        // Validate flow references exist
        const flowRefValidation = await validateFlowReferences(conditionConfig);
        if (!flowRefValidation.isValid) {
            return res.status(400).json({
                valid: false,
                error: flowRefValidation.error
            });
        }

        return res.status(200).json({
            valid: true,
            message: 'Condition configuration is valid'
        });
    } catch (error) {
        logger.error(`Error validating condition: ${error.message}`);
        return res.status(500).json({error: 'Failed to validate condition'});
    }
});

/**
 * @route GET /api/flow/starting-flows
 * @description Get all flows that can be used as starting flows
 * @access Public
 */
router.get('/starting-flows', async (req, res) => {
    try {
        const flows = await Flow.findAll({
            where: {
                isStartingFlow: true
            },
            order: [['createdAt', 'DESC']]
        });

        return res.status(200).json(flows);
    } catch (error) {
        logger.error(`Error getting starting flows: ${error.message}`);
        return res.status(500).json({error: 'Failed to get starting flows'});
    }
});

export default router;