import express from 'express';
import {v4 as uuidv4} from 'uuid';
import dialogService from '../services/dialogService.js';
import flowDialogService from '../services/flowDialogService.js';
import logger from '../utils/logger.js';
import {Dialog, Message, Flow} from '../models/index.js';

const router = express.Router();

/**
 * @route POST /api/dialog/start
 * @description Start a new dialog
 * @access Public
 */
router.post('/start', async (req, res) => {
    try {
        const result = await dialogService.startDialog();

        return res.status(201).json(result);
    } catch (error) {
        logger.error(`Error starting dialog: ${error.message}`);
        return res.status(500).json({error: 'Failed to start dialog'});
    }
});

/**
 * @route POST /api/dialog/:dialogId/message
 * @description Process a user message in a dialog
 * @access Public
 */
router.post('/:dialogId/message', async (req, res) => {
    try {
        const { dialogId } = req.params;
        const { message } = req.body;

        if (!message) {
            return res.status(400).json({ error: 'Message is required' });
        }

        // Получаем диалог, чтобы определить, есть ли у него flowId
        const dialog = await Dialog.findByPk(dialogId);
        
        if (!dialog) {
            return res.status(404).json({ error: 'Dialog not found' });
        }

        let result;

        // Обрабатываем сообщение в зависимости от типа диалога
        if (dialog.flowId && dialog.currentStep > 0) {
            // Если у диалога есть flowId и он уже начат, используем flowDialogService
            result = await flowDialogService.processMessage(dialogId, message);
        } else {
            // Иначе используем обычный dialogService
            result = await dialogService.processMessage(dialogId, message);
        }

        return res.status(200).json(result);
    } catch (error) {
        logger.error(`Error processing message: ${error.message}`);
        return res.status(500).json({error: 'Failed to process message'});
    }
});

/**
 * @route POST /api/dialog/message
 * @description Process a user message or start a new dialog
 * @access Public
 */
router.post('/message', async (req, res) => {
    try {
        const {dialogId, message} = req.body;

        if (!message) {
            return res.status(400).json({error: 'Message is required'});
        }

        let result;

        // Если dialogId не указан, создаем новый диалог
        if (!dialogId) {
            // Создаем новый диалог
            const newDialog = await dialogService.startDialog();

            // Обрабатываем первое сообщение пользователя
            result = await dialogService.processMessage(newDialog.dialogId, message);

            // Добавляем dialogId в результат
            result.dialogId = newDialog.dialogId;
            result.isNewDialog = true;
        } else {
            // Получаем существующий диалог
            const dialog = await Dialog.findByPk(dialogId);

            if (!dialog) {
                return res.status(404).json({error: 'Dialog not found'});
            }

            // Обрабатываем сообщение в зависимости от типа диалога
            if (dialog.flowId && dialog.currentStep > 0) {
                // Если у диалога есть flowId и он уже начат, используем flowDialogService
                result = await flowDialogService.processMessage(dialogId, message);
            } else {
                // Иначе используем обычный dialogService
                result = await dialogService.processMessage(dialogId, message);
            }
        }

        return res.status(200).json(result);
    } catch (error) {
        logger.error(`Error processing message: ${error.message}`);
        return res.status(500).json({ error: 'Failed to process message' });
    }
});

/**
 * @route GET /api/dialog/:dialogId
 * @description Get a dialog by ID with all messages
 * @access Public
 */
router.get('/:dialogId', async (req, res) => {
    try {
        const { dialogId } = req.params;

        const dialog = await Dialog.findByPk(dialogId, {
            include: [
                {model: Message, order: [['timestamp', 'ASC']]},
                {model: Flow}
            ]
        });

        if (!dialog) {
            return res.status(404).json({ error: 'Dialog not found' });
        }
        
        return res.status(200).json(dialog);
    } catch (error) {
        logger.error(`Error getting dialog: ${error.message}`);
        return res.status(500).json({error: 'Failed to get dialog'});
    }
});

/**
 * @route GET /api/dialog/:dialogId/messages
 * @description Get all messages from a dialog
 * @access Public
 */
router.get('/:dialogId/messages', async (req, res) => {
    try {
        const {dialogId} = req.params;

        const messages = await Message.findAll({
            where: {dialogId},
            order: [['timestamp', 'ASC']]
        });

        return res.status(200).json(messages);
    } catch (error) {
        logger.error(`Error getting messages: ${error.message}`);
        return res.status(500).json({error: 'Failed to get messages'});
    }
});

export default router;
