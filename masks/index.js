import logger from '../utils/logger.js';
import fs from 'fs';
import path from 'path';
import {fileURLToPath} from 'url';

// Получаем текущую директорию
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Хранилище для загруженных масок
const masks = {};
// Маппинг имен масок к именам файлов
const maskNameToFile = {
    'да|нет': 'yes_no'
};

// Загружаем все маски из директории
async function loadMasks() {
    try {
        const files = fs.readdirSync(__dirname);

        for (const file of files) {
            // Пропускаем index.js и не-js файлы
            if (file === 'index.js' || !file.endsWith('.js')) continue;

            // Получаем имя маски (без расширения)
            const fileName = file.replace('.js', '');

            // Динамически импортируем модуль маски
            const maskModule = await import(`./${file}`);
            const mask = maskModule.default;

            // Используем имя маски из модуля, если оно есть
            const maskName = mask.name || fileName;

            // Добавляем маску в хранилище
            masks[maskName] = mask;

            logger.info(`Loaded mask: ${maskName} from file ${fileName}.js`);
        }

        logger.info(`Total masks loaded: ${Object.keys(masks).length}`);
    } catch (error) {
        logger.error(`Error loading masks: ${error.message}`);
    }
}

// Загружаем маски при инициализации
await loadMasks();

/**
 * Получает маску по имени
 * @param {string} maskName - Имя маски
 * @returns {Object|null} - Объект маски или null, если маска не найдена
 */
export function getMask(maskName) {
    // Проверяем, есть ли маска напрямую
    if (masks[maskName]) {
        return masks[maskName];
    }

    // Проверяем, есть ли маппинг для этого имени
    const mappedName = maskNameToFile[maskName];
    if (mappedName && masks[mappedName]) {
        return masks[mappedName];
    }

    // Если маска с таким именем не найдена, ищем по свойству name
    for (const mask of Object.values(masks)) {
        if (mask.name === maskName) {
            return mask;
        }
    }

    return null;
}

/**
 * Обрабатывает ответ пользователя с помощью соответствующей маски
 * @param {string} userResponse - Ответ пользователя
 * @param {string} maskName - Имя маски
 * @param {string} botMessage - Сообщение бота
 * @returns {Promise<string|null>} - Обработанный ответ или null, если обработка не удалась
 */
export async function processMask(userResponse, maskName, botMessage) {
    const mask = getMask(maskName);

    if (!mask) {
        logger.warn(`Mask "${maskName}" not found`);
        return null;
    }

    try {
        // Сначала пробуем обработать с помощью регулярного выражения
        if (mask.handler) {
            const result = mask.handler(userResponse);
            if (result !== null) {
                logger.info(`Processed with mask handler "${maskName}": ${result}`);
                return result;
            }
        }

        // Если не удалось, используем ИИ
        if (mask.aiProcess) {
            const result = await mask.aiProcess(userResponse, botMessage);
            logger.info(`Processed with AI for mask "${maskName}": ${result}`);
            return result;
        }

        return null;
    } catch (error) {
        logger.error(`Error processing mask "${maskName}": ${error.message}`);
        return null;
    }
}

/**
 * Проверяет, является ли обработанный ответ допустимым для данной маски
 * @param {string} processedResponse - Обработанный ответ
 * @param {string} maskName - Имя маски
 * @returns {boolean} - true, если ответ допустимый, false в противном случае
 */
export function isValidResponse(processedResponse, maskName) {
    const mask = getMask(maskName);

    if (!mask) {
        logger.warn(`Mask "${maskName}" not found when validating response`);
        return true; // По умолчанию считаем ответ валидным, если маска не найдена
    }

    // Если у маски есть метод isValid, используем его
    if (mask.isValid) {
        const isValid = mask.isValid(processedResponse);
        logger.info(`Validation for mask "${maskName}": ${isValid ? 'valid' : 'invalid'}`);
        return isValid;
    }

    // По умолчанию считаем ответ валидным
    return true;
}

/**
 * Возвращает сообщение об ошибке для недопустимого ответа
 * @param {string} maskName - Имя маски
 * @param {string} botMessage - Исходное сообщение бота
 * @returns {string} - Сообщение об ошибке
 */
export function getErrorMessage(maskName, botMessage) {
    const mask = getMask(maskName);

    if (!mask || !mask.getErrorMessage) {
        // Если маска не найдена или у нее нет метода getErrorMessage,
        // возвращаем стандартное сообщение
        return `Пожалуйста, предоставьте корректный ответ. ${botMessage.replace(/\{[^}]+\}/g, '')}`;
    }

    return mask.getErrorMessage(botMessage);
}

/**
 * Извлекает имена масок из сообщения
 * @param {string} message - Сообщение с масками
 * @returns {Array<string>} - Массив имен масок
 */
export function extractMasks(message) {
    const maskRegex = /\{([^}]+)\}/g;
    const matches = [...message.matchAll(maskRegex)];
    return matches.map(match => match[1]);
}

/**
 * Удаляет маски из сообщения
 * @param {string} message - Сообщение с масками
 * @returns {string} - Сообщение без масок
 */
export function removeMasks(message) {
    return message.replace(/\{[^}]+\}/g, '');
}

export default {
    getMask,
    processMask,
    isValidResponse,
    getErrorMessage,
    extractMasks,
    removeMasks
};