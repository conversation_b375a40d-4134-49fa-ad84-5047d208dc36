import {OpenAI} from 'openai';
import logger from '../utils/logger.js';

// Инициализация клиента OpenAI с параметрами из .env
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_API_BASE_URL || undefined
});

/**
 * Обработчик маски да|нет
 */
export default {
    name: 'да|нет',
    description: 'Обрабатывает ответы типа да/нет',

    /**
     * Обработчик на основе регулярных выражений
     * @param {string} input - Ответ пользователя
     * @returns {string|null} - Обработанный ответ или null, если не удалось определить
     */
    handler(input) {
        input = input.toLowerCase().trim();

        // Слова, указывающие на положительный ответ
        const positiveWords = [
            'да', 'конечно', 'есть', 'имеется', 'присутствует',
            'имею', 'согласен', 'подтверждаю', 'верно'
        ];

        // Слова, указывающие на отрицательный ответ
        const negativeWords = [
            'нет', 'отсутствует', 'не имею', 'не взял', 'отрицаю',
            'не согласен', 'неверно', 'отказываюсь'
        ];

        // Проверяем наличие положительных слов
        if (positiveWords.some(word => input.includes(word))) {
            return 'да';
        }

        // Проверяем наличие отрицательных слов
        if (negativeWords.some(word => input.includes(word))) {
            return 'нет';
        }

        // Не удалось определить
        return null;
    },

    /**
     * Обработчик на основе ИИ
     * @param {string} input - Ответ пользователя
     * @param {string} botMessage - Сообщение бота
     * @returns {Promise<string>} - Обработанный ответ
     */
    async aiProcess(input, botMessage) {
        try {
            // Формируем промпт для модели
            const prompt = `Проанализируй ответ пользователя и определи, означает ли он "да" или "нет".
Ответ пользователя: "${input}"
Вопрос был: "${botMessage.replace(/\{[^}]+\}/g, '')}"

Если ответ означает согласие, подтверждение, положительный ответ - ответь только "да".
Если ответ означает отказ, отрицание, отсутствие - ответь только "нет".
Если ответ неоднозначный или не содержит явного да/нет - ответь "invalid_yes_no".
Ответь только "да", "нет" или "invalid_yes_no", без дополнительных пояснений.`;

            // Вызываем OpenAI
            const response = await openai.chat.completions.create({
                model: process.env.OPENAI_MODEL || 'gpt-4o',
                messages: [{role: 'user', content: prompt}],
                temperature: 0.1
            });

            // Получаем и очищаем ответ
            let result = response.choices[0].message.content.trim().toLowerCase();
            // Удаляем теги <think> и их содержимое
            result = result.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
            logger.info(`AI response: ${result}, Input: ${input}, Bot message: ${botMessage}`);
            // Проверяем результат
            if (result.includes('да')) return 'да';
            if (result.includes('нет')) return 'нет';
            if (result.includes('invalid_yes_no')) return 'invalid_yes_no';

            // Если не удалось определить, возвращаем запасной вариант
            const lowerInput = input.toLowerCase();
            if (lowerInput.includes('да')) return 'да';
            if (lowerInput.includes('нет')) return 'нет';
            return 'invalid_yes_no';
        } catch (error) {
            logger.error(`Error in AI processing for yes_no mask: ${error.message}`);
            // Запасной вариант
            const lowerInput = input.toLowerCase();
            if (lowerInput.includes('да')) return 'да';
            if (lowerInput.includes('нет')) return 'нет';
            return 'invalid_yes_no';
        }
    },

    /**
     * Проверяет, является ли обработанный ответ допустимым
     * @param {string} processedResponse - Обработанный ответ
     * @returns {boolean} - true, если ответ допустимый, false в противном случае
     */
    isValid(processedResponse) {
        // Проверяем, что ответ равен "да" или "нет"
        return processedResponse === 'да' || processedResponse === 'нет';
    },

    /**
     * Возвращает сообщение об ошибке, если ответ недопустимый
     * @param {string} botMessage - Исходное сообщение бота
     * @returns {string} - Сообщение об ошибке
     */
    getErrorMessage(botMessage) {
        return `Пожалуйста, ответьте "да" или "нет". ${botMessage.replace(/\{[^}]+\}/g, '')}`;
    }
};