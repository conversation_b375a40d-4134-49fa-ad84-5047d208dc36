import {OpenAI} from 'openai';
import logger from '../utils/logger.js';

// Инициализация клиента OpenAI с параметрами из .env
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_API_BASE_URL || undefined
});

/**
 * Обработчик маски numeric
 */
export default {
    name: 'numeric',
    description: 'Извлекает числовые значения из ответа',

    /**
     * Обработчик на основе регулярных выражений
     * @param {string} input - Ответ пользователя
     * @returns {string|null} - Обработанный ответ или null, если не удалось определить
     */
    handler(input) {
        const match = input.match(/\d+/);
        return match ? match[0] : null;
    },

    /**
     * Обработчик на основе ИИ
     * @param {string} input - Ответ пользователя
     * @param {string} botMessage - Сообщение бота
     * @returns {Promise<string>} - Обработанный ответ
     */
    async aiProcess(input, botMessage) {
        try {
            // Формируем промпт для модели
            const prompt = `Извлеки числовое значение из ответа пользователя.
Ответ пользователя: "${input}"
Вопрос был: "${botMessage.replace(/\{[^}]+\}/g, '')}"

Ответь только числом, без дополнительных пояснений. Если чисел несколько, выбери наиболее подходящее по контексту.
Если в ответе нет числа, ответь "invalid_numeric".`;

            // Вызываем OpenAI
            const response = await openai.chat.completions.create({
                model: process.env.OPENAI_MODEL || 'gpt-4o',
                messages: [{role: 'user', content: prompt}],
                temperature: 0.1
            });

            // Получаем и очищаем ответ
            let result = response.choices[0].message.content.trim();

            // Удаляем теги <think> и их содержимое
            result = result.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
            logger.info(`AI response: ${result}, Input: ${input}, Bot message: ${botMessage}`);
            // Проверяем, является ли результат числом
            const numMatch = result.match(/\d+/);
            if (numMatch) return numMatch[0];

            // Если не удалось определить, возвращаем специальное значение
            logger.warn(`Could not extract numeric value from: "${input}"`);
            return "invalid_numeric";
        } catch (error) {
            logger.error(`Error in AI processing for numeric mask: ${error.message}`);
            // Запасной вариант
            const match = input.match(/\d+/);
            return match ? match[0] : "invalid_numeric";
        }
    },

    /**
     * Проверяет, является ли обработанный ответ допустимым
     * @param {string} processedResponse - Обработанный ответ
     * @returns {boolean} - true, если ответ допустимый, false в противном случае
     */
    isValid(processedResponse) {
        // Проверяем, что ответ не равен специальному значению "invalid_numeric"
        // и что это действительно число
        return processedResponse !== "invalid_numeric" && !isNaN(Number(processedResponse));
    },

    /**
     * Возвращает сообщение об ошибке, если ответ недопустимый
     * @param {string} botMessage - Исходное сообщение бота
     * @returns {string} - Сообщение об ошибке
     */
    getErrorMessage(botMessage) {
        return `Пожалуйста, укажите числовое значение. ${botMessage.replace(/\{[^}]+\}/g, '')}`;
    }
};