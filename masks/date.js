import {OpenAI} from 'openai';
import logger from '../utils/logger.js';

// Инициализация клиента OpenAI с параметрами из .env
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_API_BASE_URL || undefined
});

/**
 * Обработчик маски date
 */
export default {
    name: 'date',
    description: 'Извлекает даты из ответа и форматирует их',

    /**
     * Обработчик на основе регулярных выражений
     * @param {string} input - Ответ пользователя
     * @returns {string|null} - Обработанный ответ или null, если не удалось определить
     */
    handler(input) {
        // Ищем дату в формате ДД.ММ.ГГГГ, ДД/ММ/ГГГГ, ДД-ММ-ГГГГ
        const dateRegex = /(\d{1,2})[\.\/\-](\d{1,2})[\.\/\-](\d{2,4})/;
        const match = input.match(dateRegex);

        if (match) {
            // Форматируем дату в ДД.ММ.ГГГГ
            return `${match[1].padStart(2, '0')}.${match[2].padStart(2, '0')}.${match[3].length === 2 ? '20' + match[3] : match[3]}`;
        }

        return null;
    },

    /**
     * Обработчик на основе ИИ
     * @param {string} input - Ответ пользователя
     * @param {string} botMessage - Сообщение бота
     * @returns {Promise<string>} - Обработанный ответ
     */
    async aiProcess(input, botMessage) {
        try {
            // Формируем промпт для модели
            const prompt = `Извлеки дату из ответа пользователя и преобразуй в формат ДД.ММ.ГГГГ.
Ответ пользователя: "${input}"
Вопрос был: "${botMessage.replace(/\{[^}]+\}/g, '')}"

Ответь только датой в формате ДД.ММ.ГГГГ, без дополнительных пояснений.
Примеры:
"15 июня 1990" -> "15.06.1990"
"5/12/2023" -> "05.12.2023"
"третье мая 2015 года" -> "03.05.2015"
Если в ответе нет даты, ответь "invalid_date".`;

            // Вызываем OpenAI
            const response = await openai.chat.completions.create({
                model: process.env.OPENAI_MODEL || 'gpt-4o',
                messages: [{role: 'user', content: prompt}],
                temperature: 0.1
            });

            // Получаем и очищаем ответ
            let result = response.choices[0].message.content.trim();

            // Удаляем теги <think> и их содержимое
            result = result.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
            logger.info(`AI response: ${result}, Input: ${input}, Bot message: ${botMessage}`);
            // Проверяем формат даты
            const dateMatch = result.match(/(\d{1,2})[\.\/\-](\d{1,2})[\.\/\-](\d{2,4})/);
            if (dateMatch) {
                return `${dateMatch[1].padStart(2, '0')}.${dateMatch[2].padStart(2, '0')}.${dateMatch[3].length === 2 ? '20' + dateMatch[3] : dateMatch[3]}`;
            }

            // Если не удалось определить, возвращаем специальное значение
            if (result === "invalid_date") {
                return "invalid_date";
            }

            // Проверяем, может быть AI вернул дату в другом формате
            const fallbackMatch = result.match(/(\d{1,2})[\.\/\-](\d{1,2})[\.\/\-](\d{2,4})/);
            return fallbackMatch ?
                `${fallbackMatch[1].padStart(2, '0')}.${fallbackMatch[2].padStart(2, '0')}.${fallbackMatch[3].length === 2 ? '20' + fallbackMatch[3] : fallbackMatch[3]}` :
                "invalid_date";
        } catch (error) {
            logger.error(`Error in AI processing for date mask: ${error.message}`);
            // Запасной вариант
            const dateRegex = /(\d{1,2})[\.\/\-](\d{1,2})[\.\/\-](\d{2,4})/;
            const match = input.match(dateRegex);
            return match ?
                `${match[1].padStart(2, '0')}.${match[2].padStart(2, '0')}.${match[3].length === 2 ? '20' + match[3] : match[3]}` :
                "invalid_date";
        }
    },

    /**
     * Проверяет, является ли обработанный ответ допустимым
     * @param {string} processedResponse - Обработанный ответ
     * @returns {boolean} - true, если ответ допустимый, false в противном случае
     */
    isValid(processedResponse) {
        // Проверяем, что ответ не равен специальному значению "invalid_date"
        // и соответствует формату даты ДД.ММ.ГГГГ
        return processedResponse !== "invalid_date" && /^\d{2}\.\d{2}\.\d{4}$/.test(processedResponse);
    },

    /**
     * Возвращает сообщение об ошибке, если ответ недопустимый
     * @param {string} botMessage - Исходное сообщение бота
     * @returns {string} - Сообщение об ошибке
     */
    getErrorMessage(botMessage) {
        return `Пожалуйста, укажите дату в формате ДД.ММ.ГГГГ. ${botMessage.replace(/\{[^}]+\}/g, '')}`;
    }
};