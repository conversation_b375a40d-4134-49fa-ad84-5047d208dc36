// Тест для проверки исправления навигации при conditional_switch
import flowDialogService from './services/flowDialogService.js';
import { Flow, FlowMessage, Dialog } from './models/index.js';
import { v4 as uuidv4 } from 'uuid';

async function testNavigationFix() {
    console.log('🧪 Testing navigation fix for conditional_switch...');
    
    try {
        // Создаем тестовый поток
        const testFlow = await Flow.create({
            id: uuidv4(),
            name: 'Test Navigation Flow',
            isStartingFlow: true,
            aiPrompt: 'Test prompt'
        });

        // Создаем сообщения
        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Первый вопрос? {да|нет}',
            step: 5
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Вы военнослужащий? {да|нет}',
            step: 6,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: { type: 'yes_no', value: 'да' },
                successAction: { goto: 'continue' },
                failAction: { goto: '#8' }
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Справка от командования? {да|нет}',
            step: 7
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Вы мужчина 18-30 лет? {да|нет}',
            step: 8,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: { type: 'yes_no', value: 'да' },
                successAction: { goto: 'continue' },
                failAction: { goto: 'fin' }
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Военный билет? {да|нет}',
            step: 9
        });

        // Создаем диалог
        const dialog = await Dialog.create({
            id: uuidv4(),
            flowId: testFlow.id,
            currentStep: 5,
            collectedData: {}
        });

        console.log('✅ Test setup completed');
        console.log('📋 Scenario: User answers "нет" to step 6, should navigate to step 8');
        console.log('   Expected: Step 8 message should be shown to user, not processed immediately');

        // Симулируем ответ "нет" на шаг 6
        const result = await flowDialogService.processMessage(dialog.id, 'нет');
        
        console.log('📊 Result:', {
            step: result.step,
            message: result.message,
            completed: result.completed,
            isConditionalSwitch: result.isConditionalSwitch
        });

        // Проверяем результат
        if (result.step === 8 && result.message.includes('Вы мужчина 18-30 лет?') && !result.completed) {
            console.log('✅ SUCCESS: Navigation works correctly!');
            console.log('   - User was navigated to step 8');
            console.log('   - Step 8 message was shown to user');
            console.log('   - Dialog is not completed yet');
            console.log('   - Conditional switch will be processed when user responds');
        } else {
            console.log('❌ FAILED: Navigation not working as expected');
            console.log('   Expected: step=8, message contains "Вы мужчина 18-30 лет?", completed=false');
            console.log('   Actual:', result);
        }

        // Cleanup
        await dialog.destroy();
        await FlowMessage.destroy({ where: { flowId: testFlow.id } });
        await testFlow.destroy();

    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
    }
}

testNavigationFix().then(() => {
    console.log('🏁 Test completed');
    process.exit(0);
}).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
});
