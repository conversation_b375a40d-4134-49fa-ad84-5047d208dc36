import { Flow } from '../models/index.js';
import logger from './logger.js';

/**
 * Validation utilities for flow management API
 */

/**
 * Valid condition types
 */
const VALID_CONDITION_TYPES = [
    'age_check',
    'yes_no',
    'equals',
    'contains',
    'numeric_compare',
    'always_true'
];

/**
 * Valid operators for different condition types
 */
const VALID_OPERATORS = {
    age_check: ['>', '<', '>=', '<=', '==', '='],
    numeric_compare: ['>', '<', '>=', '<=', '==', '=', '!=']
};

/**
 * Valid goto values patterns
 */
const VALID_GOTO_PATTERNS = [
    'continue',  // Continue current flow
    'fin',       // Finish dialog immediately
    /^#\d+$/,    // #step - jump to step within current flow
    /^[a-zA-Z0-9-_]+$/,  // flowId - jump to beginning of another flow
    /^[a-zA-Z0-9-_]+#\d+$/  // flowId#step - jump to specific step in another flow
];

/**
 * Valid message types
 */
const VALID_MESSAGE_TYPES = ['regular', 'conditional_switch'];

/**
 * Valid logic operators for multiple conditions
 */
const VALID_LOGIC_OPERATORS = ['AND', 'OR'];

/**
 * Validate isStartingFlow parameter
 * @param {any} isStartingFlow - Value to validate
 * @returns {Object} - {isValid: boolean, error?: string}
 */
export function validateIsStartingFlow(isStartingFlow) {
    if (isStartingFlow === undefined || isStartingFlow === null) {
        return { isValid: true }; // Optional field
    }
    
    if (typeof isStartingFlow !== 'boolean') {
        return { 
            isValid: false, 
            error: 'isStartingFlow must be a boolean value' 
        };
    }
    
    return { isValid: true };
}

/**
 * Validate message type
 * @param {string} messageType - Message type to validate
 * @returns {Object} - {isValid: boolean, error?: string}
 */
export function validateMessageType(messageType) {
    if (messageType === undefined || messageType === null) {
        return { isValid: true }; // Optional field, defaults to 'regular'
    }
    
    if (!VALID_MESSAGE_TYPES.includes(messageType)) {
        return {
            isValid: false,
            error: `messageType must be one of: ${VALID_MESSAGE_TYPES.join(', ')}`
        };
    }
    
    return { isValid: true };
}

/**
 * Validate a single condition
 * @param {Object} condition - Condition object to validate
 * @returns {Object} - {isValid: boolean, error?: string}
 */
export function validateCondition(condition) {
    if (!condition || typeof condition !== 'object') {
        return { isValid: false, error: 'Condition must be an object' };
    }

    const { type, operator, value } = condition;

    // Validate condition type
    if (!type || !VALID_CONDITION_TYPES.includes(type)) {
        return {
            isValid: false,
            error: `Condition type must be one of: ${VALID_CONDITION_TYPES.join(', ')}`
        };
    }

    // Validate operator for types that require it
    if (['age_check', 'numeric_compare'].includes(type)) {
        if (!operator || !VALID_OPERATORS[type].includes(operator)) {
            return {
                isValid: false,
                error: `Operator for ${type} must be one of: ${VALID_OPERATORS[type].join(', ')}`
            };
        }
    }

    // Validate value is provided (except for always_true)
    if (type !== 'always_true' && (value === undefined || value === null)) {
        return { isValid: false, error: 'Condition value is required' };
    }

    // always_true doesn't need value or operator
    if (type === 'always_true') {
        return { isValid: true };
    }

    // Validate numeric values for age_check and numeric_compare
    if (['age_check', 'numeric_compare'].includes(type)) {
        if (typeof value !== 'number' || isNaN(value)) {
            return {
                isValid: false,
                error: `Value for ${type} must be a valid number`
            };
        }
    }

    // Validate yes_no values
    if (type === 'yes_no') {
        if (!['да', 'нет'].includes(value)) {
            return {
                isValid: false,
                error: 'Value for yes_no condition must be "да" or "нет"'
            };
        }
    }

    return { isValid: true };
}

/**
 * Validate goto value format
 * @param {string} gotoValue - Goto value to validate
 * @returns {Object} - {isValid: boolean, error?: string}
 */
export function validateGoto(gotoValue) {
    if (!gotoValue || typeof gotoValue !== 'string') {
        return { isValid: false, error: 'goto must be a non-empty string' };
    }

    const trimmedValue = gotoValue.trim();

    // Check against valid patterns
    const isValid = VALID_GOTO_PATTERNS.some(pattern => {
        if (typeof pattern === 'string') {
            return trimmedValue === pattern;
        } else {
            return pattern.test(trimmedValue);
        }
    });

    if (!isValid) {
        return {
            isValid: false,
            error: 'goto must be one of: "continue", "fin", "#step", "flowId", or "flowId#step"'
        };
    }

    return { isValid: true };
}

/**
 * Validate action object (successAction or failAction)
 * @param {Object} action - Action object to validate
 * @param {string} actionName - Name of the action (for error messages)
 * @returns {Object} - {isValid: boolean, error?: string}
 */
export function validateAction(action, actionName) {
    if (!action || typeof action !== 'object') {
        return { isValid: false, error: `${actionName} must be an object` };
    }

    if (!action.goto) {
        return { isValid: false, error: `${actionName} must contain "goto" field` };
    }

    const gotoValidation = validateGoto(action.goto);
    if (!gotoValidation.isValid) {
        return {
            isValid: false,
            error: `${actionName}.goto: ${gotoValidation.error}`
        };
    }

    return { isValid: true };
}

/**
 * Validate condition configuration for conditional_switch messages
 * @param {Object} conditionConfig - Condition configuration to validate
 * @returns {Object} - {isValid: boolean, error?: string}
 */
export function validateConditionConfig(conditionConfig) {
    if (!conditionConfig || typeof conditionConfig !== 'object') {
        return { isValid: false, error: 'conditionConfig must be an object' };
    }

    const { condition, successAction, failAction, successFlowId, failFlowId } = conditionConfig;

    // Must have condition
    if (!condition) {
        return {
            isValid: false,
            error: 'conditionConfig must specify "condition"'
        };
    }

    // Validate condition
    const conditionValidation = validateCondition(condition);
    if (!conditionValidation.isValid) {
        return conditionValidation;
    }

    // Check for new format (successAction/failAction) vs old format (successFlowId/failFlowId)
    const hasNewFormat = successAction || failAction;
    const hasOldFormat = successFlowId !== undefined || failFlowId !== undefined;

    if (hasNewFormat && hasOldFormat) {
        return {
            isValid: false,
            error: 'Cannot mix old format (successFlowId/failFlowId) with new format (successAction/failAction)'
        };
    }

    if (hasNewFormat) {
        // Validate new format
        if (!successAction || !failAction) {
            return {
                isValid: false,
                error: 'Both successAction and failAction are required in new format'
            };
        }

        const successValidation = validateAction(successAction, 'successAction');
        if (!successValidation.isValid) {
            return successValidation;
        }

        const failValidation = validateAction(failAction, 'failAction');
        if (!failValidation.isValid) {
            return failValidation;
        }
    } else {
        // Support old format for backward compatibility
        if (successFlowId !== undefined && successFlowId !== null && typeof successFlowId !== 'string') {
            return {
                isValid: false,
                error: 'successFlowId must be a string (flow ID or "continue") or omitted'
            };
        }

        if (failFlowId !== undefined && failFlowId !== null && typeof failFlowId !== 'string') {
            return {
                isValid: false,
                error: 'failFlowId must be a string (flow ID or "continue") or omitted'
            };
        }
    }

    return { isValid: true };
}

/**
 * Extract flow ID from goto value
 * @param {string} gotoValue - Goto value
 * @returns {string|null} - Flow ID or null if not a flow reference
 */
function extractFlowIdFromGoto(gotoValue) {
    if (!gotoValue || typeof gotoValue !== 'string') {
        return null;
    }

    const trimmed = gotoValue.trim();

    // Skip special values
    if (trimmed === 'continue' || trimmed === 'fin' || trimmed.startsWith('#')) {
        return null;
    }

    // Extract flow ID from "flowId" or "flowId#step"
    if (trimmed.includes('#')) {
        return trimmed.split('#')[0];
    }

    return trimmed;
}

/**
 * Validate that referenced flow IDs exist in the database
 * @param {Object} conditionConfig - Condition configuration
 * @returns {Promise<Object>} - {isValid: boolean, error?: string}
 */
export async function validateFlowReferences(conditionConfig) {
    const { successAction, failAction, successFlowId, failFlowId } = conditionConfig;

    try {
        const flowIdsToCheck = [];

        // Handle new format
        if (successAction && successAction.goto) {
            const flowId = extractFlowIdFromGoto(successAction.goto);
            if (flowId) {
                flowIdsToCheck.push({ id: flowId, source: 'successAction.goto' });
            }
        }

        if (failAction && failAction.goto) {
            const flowId = extractFlowIdFromGoto(failAction.goto);
            if (flowId) {
                flowIdsToCheck.push({ id: flowId, source: 'failAction.goto' });
            }
        }

        // Handle old format for backward compatibility
        if (successFlowId && successFlowId !== 'continue') {
            flowIdsToCheck.push({ id: successFlowId, source: 'successFlowId' });
        }

        if (failFlowId && failFlowId !== 'continue') {
            flowIdsToCheck.push({ id: failFlowId, source: 'failFlowId' });
        }

        // Check all referenced flows exist
        for (const { id, source } of flowIdsToCheck) {
            const flow = await Flow.findByPk(id);
            if (!flow) {
                return {
                    isValid: false,
                    error: `Referenced flow with ID "${id}" (from ${source}) does not exist`
                };
            }
        }

        return { isValid: true };
    } catch (error) {
        logger.error(`Error validating flow references: ${error.message}`);
        return {
            isValid: false,
            error: 'Failed to validate flow references'
        };
    }
}

/**
 * Validate flow message data for creation/update
 * @param {Object} messageData - Message data to validate
 * @param {boolean} isUpdate - Whether this is an update operation
 * @returns {Promise<Object>} - {isValid: boolean, error?: string}
 */
export async function validateFlowMessageData(messageData, isUpdate = false) {
    const { content, step, messageType, conditionConfig } = messageData;
    
    // Validate required fields for creation
    if (!isUpdate) {
        if (!content) {
            return { isValid: false, error: 'content is required' };
        }
        if (step === undefined) {
            return { isValid: false, error: 'step is required' };
        }
    }
    
    // Validate step is a number
    if (step !== undefined && (typeof step !== 'number' || step < 0)) {
        return { isValid: false, error: 'step must be a non-negative number' };
    }
    
    // Validate message type
    const messageTypeValidation = validateMessageType(messageType);
    if (!messageTypeValidation.isValid) {
        return messageTypeValidation;
    }
    
    // Validate conditional configuration
    if (messageType === 'conditional_switch') {
        if (!conditionConfig) {
            return {
                isValid: false,
                error: 'conditionConfig is required for conditional_switch messages'
            };
        }
        
        const configValidation = validateConditionConfig(conditionConfig);
        if (!configValidation.isValid) {
            return configValidation;
        }
        
        // Validate flow references exist
        const flowRefValidation = await validateFlowReferences(conditionConfig);
        if (!flowRefValidation.isValid) {
            return flowRefValidation;
        }
    } else if (conditionConfig) {
        return {
            isValid: false,
            error: 'conditionConfig can only be provided for conditional_switch messages'
        };
    }
    
    return { isValid: true };
}
